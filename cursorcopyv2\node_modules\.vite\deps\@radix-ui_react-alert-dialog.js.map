{"version": 3, "sources": ["../../@radix-ui/react-alert-dialog/src/AlertDialog.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport { createDialogScope } from '@radix-ui/react-dialog';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { Slottable } from '@radix-ui/react-slot';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst ROOT_NAME = 'AlertDialog';\n\ntype ScopedProps<P> = P & { __scopeAlertDialog?: Scope };\nconst [createAlertDialogContext, createAlertDialogScope] = createContextScope(ROOT_NAME, [\n  createDialogScope,\n]);\nconst useDialogScope = createDialogScope();\n\ntype DialogProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Root>;\ninterface AlertDialogProps extends Omit<DialogProps, 'modal'> {}\n\nconst AlertDialog: React.FC<AlertDialogProps> = (props: ScopedProps<AlertDialogProps>) => {\n  const { __scopeAlertDialog, ...alertDialogProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Root {...dialogScope} {...alertDialogProps} modal={true} />;\n};\n\nAlertDialog.displayName = ROOT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogTrigger\n * -----------------------------------------------------------------------------------------------*/\nconst TRIGGER_NAME = 'AlertDialogTrigger';\n\ntype AlertDialogTriggerElement = React.ElementRef<typeof DialogPrimitive.Trigger>;\ntype DialogTriggerProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Trigger>;\ninterface AlertDialogTriggerProps extends DialogTriggerProps {}\n\nconst AlertDialogTrigger = React.forwardRef<AlertDialogTriggerElement, AlertDialogTriggerProps>(\n  (props: ScopedProps<AlertDialogTriggerProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...triggerProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Trigger {...dialogScope} {...triggerProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogTrigger.displayName = TRIGGER_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogPortal\n * -----------------------------------------------------------------------------------------------*/\n\nconst PORTAL_NAME = 'AlertDialogPortal';\n\ntype DialogPortalProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Portal>;\ninterface AlertDialogPortalProps extends DialogPortalProps {}\n\nconst AlertDialogPortal: React.FC<AlertDialogPortalProps> = (\n  props: ScopedProps<AlertDialogPortalProps>\n) => {\n  const { __scopeAlertDialog, ...portalProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Portal {...dialogScope} {...portalProps} />;\n};\n\nAlertDialogPortal.displayName = PORTAL_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogOverlay\n * -----------------------------------------------------------------------------------------------*/\n\nconst OVERLAY_NAME = 'AlertDialogOverlay';\n\ntype AlertDialogOverlayElement = React.ElementRef<typeof DialogPrimitive.Overlay>;\ntype DialogOverlayProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay>;\ninterface AlertDialogOverlayProps extends DialogOverlayProps {}\n\nconst AlertDialogOverlay = React.forwardRef<AlertDialogOverlayElement, AlertDialogOverlayProps>(\n  (props: ScopedProps<AlertDialogOverlayProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...overlayProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Overlay {...dialogScope} {...overlayProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogOverlay.displayName = OVERLAY_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogContent\n * -----------------------------------------------------------------------------------------------*/\n\nconst CONTENT_NAME = 'AlertDialogContent';\n\ntype AlertDialogContentContextValue = {\n  cancelRef: React.MutableRefObject<AlertDialogCancelElement | null>;\n};\n\nconst [AlertDialogContentProvider, useAlertDialogContentContext] =\n  createAlertDialogContext<AlertDialogContentContextValue>(CONTENT_NAME);\n\ntype AlertDialogContentElement = React.ElementRef<typeof DialogPrimitive.Content>;\ntype DialogContentProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content>;\ninterface AlertDialogContentProps\n  extends Omit<DialogContentProps, 'onPointerDownOutside' | 'onInteractOutside'> {}\n\nconst AlertDialogContent = React.forwardRef<AlertDialogContentElement, AlertDialogContentProps>(\n  (props: ScopedProps<AlertDialogContentProps>, forwardedRef) => {\n    const { __scopeAlertDialog, children, ...contentProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const contentRef = React.useRef<AlertDialogContentElement>(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const cancelRef = React.useRef<AlertDialogCancelElement | null>(null);\n\n    return (\n      <DialogPrimitive.WarningProvider\n        contentName={CONTENT_NAME}\n        titleName={TITLE_NAME}\n        docsSlug=\"alert-dialog\"\n      >\n        <AlertDialogContentProvider scope={__scopeAlertDialog} cancelRef={cancelRef}>\n          <DialogPrimitive.Content\n            role=\"alertdialog\"\n            {...dialogScope}\n            {...contentProps}\n            ref={composedRefs}\n            onOpenAutoFocus={composeEventHandlers(contentProps.onOpenAutoFocus, (event) => {\n              event.preventDefault();\n              cancelRef.current?.focus({ preventScroll: true });\n            })}\n            onPointerDownOutside={(event) => event.preventDefault()}\n            onInteractOutside={(event) => event.preventDefault()}\n          >\n            {/**\n             * We have to use `Slottable` here as we cannot wrap the `AlertDialogContentProvider`\n             * around everything, otherwise the `DescriptionWarning` would be rendered straight away.\n             * This is because we want the accessibility checks to run only once the content is actually\n             * open and that behaviour is already encapsulated in `DialogContent`.\n             */}\n            <Slottable>{children}</Slottable>\n            {process.env.NODE_ENV === 'development' && (\n              <DescriptionWarning contentRef={contentRef} />\n            )}\n          </DialogPrimitive.Content>\n        </AlertDialogContentProvider>\n      </DialogPrimitive.WarningProvider>\n    );\n  }\n);\n\nAlertDialogContent.displayName = CONTENT_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogTitle\n * -----------------------------------------------------------------------------------------------*/\n\nconst TITLE_NAME = 'AlertDialogTitle';\n\ntype AlertDialogTitleElement = React.ElementRef<typeof DialogPrimitive.Title>;\ntype DialogTitleProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>;\ninterface AlertDialogTitleProps extends DialogTitleProps {}\n\nconst AlertDialogTitle = React.forwardRef<AlertDialogTitleElement, AlertDialogTitleProps>(\n  (props: ScopedProps<AlertDialogTitleProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...titleProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Title {...dialogScope} {...titleProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogTitle.displayName = TITLE_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogDescription\n * -----------------------------------------------------------------------------------------------*/\n\nconst DESCRIPTION_NAME = 'AlertDialogDescription';\n\ntype AlertDialogDescriptionElement = React.ElementRef<typeof DialogPrimitive.Description>;\ntype DialogDescriptionProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>;\ninterface AlertDialogDescriptionProps extends DialogDescriptionProps {}\n\nconst AlertDialogDescription = React.forwardRef<\n  AlertDialogDescriptionElement,\n  AlertDialogDescriptionProps\n>((props: ScopedProps<AlertDialogDescriptionProps>, forwardedRef) => {\n  const { __scopeAlertDialog, ...descriptionProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return <DialogPrimitive.Description {...dialogScope} {...descriptionProps} ref={forwardedRef} />;\n});\n\nAlertDialogDescription.displayName = DESCRIPTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogAction\n * -----------------------------------------------------------------------------------------------*/\n\nconst ACTION_NAME = 'AlertDialogAction';\n\ntype AlertDialogActionElement = React.ElementRef<typeof DialogPrimitive.Close>;\ntype DialogCloseProps = React.ComponentPropsWithoutRef<typeof DialogPrimitive.Close>;\ninterface AlertDialogActionProps extends DialogCloseProps {}\n\nconst AlertDialogAction = React.forwardRef<AlertDialogActionElement, AlertDialogActionProps>(\n  (props: ScopedProps<AlertDialogActionProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...actionProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return <DialogPrimitive.Close {...dialogScope} {...actionProps} ref={forwardedRef} />;\n  }\n);\n\nAlertDialogAction.displayName = ACTION_NAME;\n\n/* -------------------------------------------------------------------------------------------------\n * AlertDialogCancel\n * -----------------------------------------------------------------------------------------------*/\n\nconst CANCEL_NAME = 'AlertDialogCancel';\n\ntype AlertDialogCancelElement = React.ElementRef<typeof DialogPrimitive.Close>;\ninterface AlertDialogCancelProps extends DialogCloseProps {}\n\nconst AlertDialogCancel = React.forwardRef<AlertDialogCancelElement, AlertDialogCancelProps>(\n  (props: ScopedProps<AlertDialogCancelProps>, forwardedRef) => {\n    const { __scopeAlertDialog, ...cancelProps } = props;\n    const { cancelRef } = useAlertDialogContentContext(CANCEL_NAME, __scopeAlertDialog);\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const ref = useComposedRefs(forwardedRef, cancelRef);\n    return <DialogPrimitive.Close {...dialogScope} {...cancelProps} ref={ref} />;\n  }\n);\n\nAlertDialogCancel.displayName = CANCEL_NAME;\n\n/* ---------------------------------------------------------------------------------------------- */\n\ntype DescriptionWarningProps = {\n  contentRef: React.RefObject<AlertDialogContentElement>;\n};\n\nconst DescriptionWarning: React.FC<DescriptionWarningProps> = ({ contentRef }) => {\n  const MESSAGE = `\\`${CONTENT_NAME}\\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \\`${CONTENT_NAME}\\` by passing a \\`${DESCRIPTION_NAME}\\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \\`id\\` and passing the same value to the \\`aria-describedby\\` prop in \\`${CONTENT_NAME}\\`. If the description is confusing or duplicative for sighted users, you can use the \\`@radix-ui/react-visually-hidden\\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;\n\n  React.useEffect(() => {\n    const hasDescription = document.getElementById(\n      contentRef.current?.getAttribute('aria-describedby')!\n    );\n    if (!hasDescription) console.warn(MESSAGE);\n  }, [MESSAGE, contentRef]);\n\n  return null;\n};\n\nconst Root = AlertDialog;\nconst Trigger = AlertDialogTrigger;\nconst Portal = AlertDialogPortal;\nconst Overlay = AlertDialogOverlay;\nconst Content = AlertDialogContent;\nconst Action = AlertDialogAction;\nconst Cancel = AlertDialogCancel;\nconst Title = AlertDialogTitle;\nconst Description = AlertDialogDescription;\n\nexport {\n  createAlertDialogScope,\n  //\n  AlertDialog,\n  AlertDialogTrigger,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogContent,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  //\n  Root,\n  Trigger,\n  Portal,\n  Overlay,\n  Content,\n  Action,\n  Cancel,\n  Title,\n  Description,\n};\nexport type {\n  AlertDialogProps,\n  AlertDialogTriggerProps,\n  AlertDialogPortalProps,\n  AlertDialogOverlayProps,\n  AlertDialogContentProps,\n  AlertDialogActionProps,\n  AlertDialogCancelProps,\n  AlertDialogTitleProps,\n  AlertDialogDescriptionProps,\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,YAAuB;AA4Bd,yBAAA;AAdT,IAAM,YAAY;AAGlB,IAAM,CAAC,0BAA0B,sBAAsB,IAAI,mBAAmB,WAAW;EACvF;AACF,CAAC;AACD,IAAM,iBAAiB,kBAAkB;AAKzC,IAAM,cAA0C,CAAC,UAAyC;AACxF,QAAM,EAAE,oBAAoB,GAAG,iBAAiB,IAAI;AACpD,QAAM,cAAc,eAAe,kBAAkB;AACrD,aAAO,wBAAiB,MAAhB,EAAsB,GAAG,aAAc,GAAG,kBAAkB,OAAO,KAAA,CAAM;AACnF;AAEA,YAAY,cAAc;AAK1B,IAAM,eAAe;AAMrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,GAAG,aAAa,IAAI;AAChD,UAAM,cAAc,eAAe,kBAAkB;AACrD,eAAO,wBAAiB,SAAhB,EAAyB,GAAG,aAAc,GAAG,cAAc,KAAK,aAAA,CAAc;EACxF;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,cAAc;AAKpB,IAAM,oBAAsD,CAC1D,UACG;AACH,QAAM,EAAE,oBAAoB,GAAG,YAAY,IAAI;AAC/C,QAAM,cAAc,eAAe,kBAAkB;AACrD,aAAO,wBAAiB,QAAhB,EAAwB,GAAG,aAAc,GAAG,YAAA,CAAa;AACnE;AAEA,kBAAkB,cAAc;AAMhC,IAAM,eAAe;AAMrB,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,GAAG,aAAa,IAAI;AAChD,UAAM,cAAc,eAAe,kBAAkB;AACrD,eAAO,wBAAiB,SAAhB,EAAyB,GAAG,aAAc,GAAG,cAAc,KAAK,aAAA,CAAc;EACxF;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,eAAe;AAMrB,IAAM,CAAC,4BAA4B,4BAA4B,IAC7D,yBAAyD,YAAY;AAOvE,IAAM,qBAA2B;EAC/B,CAAC,OAA6C,iBAAiB;AAC7D,UAAM,EAAE,oBAAoB,UAAU,GAAG,aAAa,IAAI;AAC1D,UAAM,cAAc,eAAe,kBAAkB;AACrD,UAAM,aAAmB,aAAkC,IAAI;AAC/D,UAAM,eAAe,gBAAgB,cAAc,UAAU;AAC7D,UAAM,YAAkB,aAAwC,IAAI;AAEpE,eACE;MAAiB;MAAhB;QACC,aAAa;QACb,WAAW;QACX,UAAS;QAET,cAAA,wBAAC,4BAAA,EAA2B,OAAO,oBAAoB,WACrD,cAAA;UAAiB;UAAhB;YACC,MAAK;YACJ,GAAG;YACH,GAAG;YACJ,KAAK;YACL,iBAAiB,qBAAqB,aAAa,iBAAiB,CAAC,UAAU;;AAC7E,oBAAM,eAAe;AACrB,8BAAU,YAAV,mBAAmB,MAAM,EAAE,eAAe,KAAK;YACjD,CAAC;YACD,sBAAsB,CAAC,UAAU,MAAM,eAAe;YACtD,mBAAmB,CAAC,UAAU,MAAM,eAAe;YAQnD,UAAA;kBAAA,wBAAC,WAAA,EAAW,SAAA,CAAS;kBAEnB,wBAAC,oBAAA,EAAmB,WAAA,CAAwB;YAAA;UAAA;QAEhD,EAAA,CACF;MAAA;IACF;EAEJ;AACF;AAEA,mBAAmB,cAAc;AAMjC,IAAM,aAAa;AAMnB,IAAM,mBAAyB;EAC7B,CAAC,OAA2C,iBAAiB;AAC3D,UAAM,EAAE,oBAAoB,GAAG,WAAW,IAAI;AAC9C,UAAM,cAAc,eAAe,kBAAkB;AACrD,eAAO,wBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,YAAY,KAAK,aAAA,CAAc;EACpF;AACF;AAEA,iBAAiB,cAAc;AAM/B,IAAM,mBAAmB;AAMzB,IAAM,yBAA+B,iBAGnC,CAAC,OAAiD,iBAAiB;AACnE,QAAM,EAAE,oBAAoB,GAAG,iBAAiB,IAAI;AACpD,QAAM,cAAc,eAAe,kBAAkB;AACrD,aAAO,wBAAiB,aAAhB,EAA6B,GAAG,aAAc,GAAG,kBAAkB,KAAK,aAAA,CAAc;AAChG,CAAC;AAED,uBAAuB,cAAc;AAMrC,IAAM,cAAc;AAMpB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,oBAAoB,GAAG,YAAY,IAAI;AAC/C,UAAM,cAAc,eAAe,kBAAkB;AACrD,eAAO,wBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,aAAa,KAAK,aAAA,CAAc;EACrF;AACF;AAEA,kBAAkB,cAAc;AAMhC,IAAM,cAAc;AAKpB,IAAM,oBAA0B;EAC9B,CAAC,OAA4C,iBAAiB;AAC5D,UAAM,EAAE,oBAAoB,GAAG,YAAY,IAAI;AAC/C,UAAM,EAAE,UAAU,IAAI,6BAA6B,aAAa,kBAAkB;AAClF,UAAM,cAAc,eAAe,kBAAkB;AACrD,UAAM,MAAM,gBAAgB,cAAc,SAAS;AACnD,eAAO,wBAAiB,OAAhB,EAAuB,GAAG,aAAc,GAAG,aAAa,IAAA,CAAU;EAC5E;AACF;AAEA,kBAAkB,cAAc;AAQhC,IAAM,qBAAwD,CAAC,EAAE,WAAW,MAAM;AAChF,QAAM,UAAU,KAAK,YAAY;;qCAEE,YAAY,qBAAqB,gBAAgB;;4JAEsE,YAAY;;;AAIhK,EAAA,gBAAU,MAAM;;AACpB,UAAM,iBAAiB,SAAS;OAC9B,gBAAW,YAAX,mBAAoB,aAAa;IACnC;AACA,QAAI,CAAC,eAAgB,SAAQ,KAAK,OAAO;EAC3C,GAAG,CAAC,SAAS,UAAU,CAAC;AAExB,SAAO;AACT;AAEA,IAAMA,QAAO;AACb,IAAMC,WAAU;AAChB,IAAMC,UAAS;AACf,IAAMC,WAAU;AAChB,IAAMC,WAAU;AAChB,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAMC,SAAQ;AACd,IAAMC,eAAc;", "names": ["Root", "<PERSON><PERSON>", "Portal", "Overlay", "Content", "Title", "Description"]}