import React from 'react';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { Trash2 } from 'lucide-react';
import { EstimateItem, useEstimate } from '@/contexts/EstimateContext';
import { decimalToFraction } from '@/utils/measurementUtils';

// Helper function to format currency
const formatCurrency = (amount: number): string => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
};

// Helper function to calculate square footage
const calculateSqFt = (width: number, height: number): number => {
  return (width * height) / 144; // Convert square inches to square feet
};

const EstimateItemTable: React.FC = () => {
  const { estimateItems, removeEstimateItem } = useEstimate();

  // If there are no items, show a message
  if (estimateItems.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No items in estimate. Add items from the Glass Types tab.
      </div>
    );
  }

  return (
    <div className="overflow-x-auto">
      <Table className="w-full">
        <TableHeader>
          <TableRow>
            <TableHead>Glass Type</TableHead>
            <TableHead>Thickness</TableHead>
            <TableHead>Dimensions (W × H)</TableHead>
            <TableHead className="text-right">Qty</TableHead>
            <TableHead className="text-right">Sq Ft (per piece / total)</TableHead>
            <TableHead className="text-right">Weight</TableHead>
            <TableHead className="text-right">Price</TableHead>
            <TableHead className="text-center">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {estimateItems.map((item) => {
            // Calculate square footage
            const width = item.adjustedWidth || item.width;
            const height = item.adjustedHeight || item.height;
            const sqFt = calculateSqFt(width, height);
            const totalSqFt = sqFt * item.quantity;

            // Calculate weight
            const weightPerPiece = sqFt * item.glassType.lbsPerSqFt;
            const totalWeight = weightPerPiece * item.quantity;

            // Calculate price
            const pricePerPiece = sqFt * item.glassType.price;
            const totalPrice = pricePerPiece * item.quantity;

            return (
              <TableRow key={item.id}>
                <TableCell className="font-medium">{item.glassType.name}</TableCell>
                <TableCell>{item.glassType.thicknessStr || item.glassType.thickness}</TableCell>
                <TableCell>
                  {decimalToFraction(width)}" × {decimalToFraction(height)}"
                  {item.measurementType === 'DLO' && (
                    <span className="text-xs text-muted-foreground ml-1">(GS)</span>
                  )}
                </TableCell>
                <TableCell className="text-right">{item.quantity}</TableCell>
                <TableCell className="text-right">{sqFt.toFixed(2)} / {totalSqFt.toFixed(2)}</TableCell>
                <TableCell className="text-right">{totalWeight.toFixed(2)} lbs</TableCell>
                <TableCell className="text-right">{formatCurrency(totalPrice)}</TableCell>
                <TableCell className="text-center">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => removeEstimateItem(item.id)}
                    className="h-8 w-8 text-red-500 hover:text-red-700 hover:bg-red-100/10"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            );
          })}
        </TableBody>
      </Table>
    </div>
  );
};

export default EstimateItemTable;
