import { Dimension } from '../context/GlassContext';
import { useMemo } from 'react';

export const MEASUREMENT_CONSTANTS = {
  INCHES_PER_FOOT: 12,
  MIN_DIMENSION: 0,
  FRACTION_PRECISION: 1e-6,
} as const;

export type FractionOption = {
  value: string;
  label: string;
  decimal: number;
};

export const fractionOptions: readonly FractionOption[] = [
  { value: '0', label: '0', decimal: 0 },
  { value: '1/16', label: '1/16', decimal: 1/16 },
  { value: '1/8', label: '1/8', decimal: 1/8 },
  { value: '3/16', label: '3/16', decimal: 3/16 },
  { value: '1/4', label: '1/4', decimal: 1/4 },
  { value: '5/16', label: '5/16', decimal: 5/16 },
  { value: '3/8', label: '3/8', decimal: 3/8 },
  { value: '7/16', label: '7/16', decimal: 7/16 },
  { value: '1/2', label: '1/2', decimal: 1/2 },
  { value: '9/16', label: '9/16', decimal: 9/16 },
  { value: '5/8', label: '5/8', decimal: 5/8 },
  { value: '11/16', label: '11/16', decimal: 11/16 },
  { value: '3/4', label: '3/4', decimal: 3/4 },
  { value: '13/16', label: '13/16', decimal: 13/16 },
  { value: '7/8', label: '7/8', decimal: 7/8 },
  { value: '15/16', label: '15/16', decimal: 15/16 },
] as const;

export type MeasurementOptions = {
  thickness: string;
  measurementType: 'DLO' | 'GS';
  system: 'Storefront' | 'Curtainwall' | 'SSG' | 'Comm. Insert' | 'Res.Insert' | 'Mirror' | 'Residential IG';
  allGlassSameMake?: boolean;
};

/**
 * Converts a fraction string to its decimal value
 * @param fraction The fraction string to convert
 * @returns The decimal value of the fraction
 * @throws {Error} If the fraction is invalid
 */
export const fractionToDecimal = (fraction: string): number => {
  // Early validation
  if (!fraction) {
    console.warn('Empty fraction in fractionToDecimal');
    return 0;
  }

  if (fraction === '0') return 0;

  try {
    // Try to find the fraction in the predefined options
    const option = fractionOptions.find(opt => opt.value === fraction);
    if (option) return option.decimal;

    // If not found in options, try to parse it
    const parts = fraction.split('/');
    if (parts.length !== 2) {
      console.warn('Invalid fraction format:', fraction);
      return 0;
    }

    const numerator = parseFloat(parts[0]);
    const denominator = parseFloat(parts[1]);

    // Validate parsed values
    if (isNaN(numerator) || isNaN(denominator) || denominator === 0) {
      console.warn('Invalid fraction values:', { numerator, denominator });
      return 0;
    }

    const result = numerator / denominator;

    // Final validation
    if (isNaN(result) || result < 0) {
      console.warn('Invalid fraction calculation:', { numerator, denominator, result });
      return 0;
    }

    return result;
  } catch (error) {
    console.error('Error converting fraction to decimal:', error);
    return 0;
  }
};

/**
 * Validates a dimension object
 * @param dimension The dimension to validate
 * @throws {Error} If the dimension is invalid
 */
const validateDimension = (dimension: Dimension): void => {
  if (!dimension) throw new Error('Dimension cannot be null');

  // Allow zero feet but require at least 1 inch
  if (dimension.inches < 1 && dimension.feet === 0) {
    throw new Error('Dimension must be at least 1 inch');
  }

  if (dimension.inches < 0) {
    throw new Error('Inches cannot be negative');
  }

  if (!fractionOptions.some(opt => opt.value === dimension.fraction)) {
    throw new Error('Invalid fraction value');
  }
};

/**
 * Converts a dimension to decimal feet
 * @param dimension The dimension to convert
 * @returns The dimension in decimal feet
 */
export const dimensionToFeet = (dimension: Dimension): number => {
  try {
    // Early validation
    if (!dimension) {
      console.warn('Missing dimension in dimensionToFeet');
      return 0;
    }

    // Validate dimension
    validateDimension(dimension);

    // Convert fraction to decimal with validation
    const fractionDecimal = fractionToDecimal(dimension.fraction);
    if (isNaN(fractionDecimal)) {
      console.warn('Invalid fraction conversion:', dimension.fraction);
      return 0;
    }

    // Calculate total inches with validation
    const totalInches = dimension.inches + fractionDecimal;
    if (isNaN(totalInches) || totalInches < 0) {
      console.warn('Invalid total inches calculation:', { inches: dimension.inches, fraction: dimension.fraction, totalInches });
      return 0;
    }

    // Convert to feet
    const feet = totalInches / MEASUREMENT_CONSTANTS.INCHES_PER_FOOT;

    // Final validation
    if (isNaN(feet) || feet < 0) {
      console.warn('Invalid feet conversion:', { totalInches, feet });
      return 0;
    }

    return feet;
  } catch (error) {
    console.error('Error converting dimension to feet:', error);
    return 0;
  }
};

/**
 * Hook to calculate area in square feet with memoization
 */
export const useCalculateAreaSqFt = (width: Dimension, height: Dimension) => {
  return useMemo(() => {
    try {
      validateDimension(width);
      validateDimension(height);
      const widthFeet = dimensionToFeet(width);
      const heightFeet = dimensionToFeet(height);
      return widthFeet * heightFeet;
    } catch (error) {
      console.error('Error calculating area:', error);
      return 0;
    }
  }, [width, height]);
};

/**
 * Calculates area in square feet
 * @param width Width dimension (can be Dimension object or number)
 * @param height Height dimension (can be Dimension object or number)
 * @returns Area in square feet
 */
export const calculateAreaSqFt = (width: Dimension | number, height: Dimension | number): number => {
  try {
    // Handle different dimension formats
    if (typeof width === 'number' && typeof height === 'number') {
      // Simple calculation for numeric dimensions (assumed to be in inches)
      const widthFeet = width / MEASUREMENT_CONSTANTS.INCHES_PER_FOOT;
      const heightFeet = height / MEASUREMENT_CONSTANTS.INCHES_PER_FOOT;

      // Check for invalid values
      if (isNaN(widthFeet) || isNaN(heightFeet) || widthFeet <= 0 || heightFeet <= 0) {
        console.warn('Invalid numeric dimension values:', { width, height });
        return 0;
      }

      return widthFeet * heightFeet;
    } else if (typeof width === 'object' && typeof height === 'object') {
      // Check if dimensions are properly initialized
      if (!width || !height ||
          typeof width.inches !== 'number' || typeof height.inches !== 'number' ||
          isNaN(width.inches) || isNaN(height.inches)) {
        console.warn('Invalid dimension conversion: dimensions not properly initialized', { width, height });
        return 0;
      }

      // Validate dimensions
      validateDimension(width);
      validateDimension(height);

      // Convert to feet with validation
      const widthFeet = dimensionToFeet(width);
      const heightFeet = dimensionToFeet(height);

      // Check for invalid conversions - allow very small values but not zero
      if (isNaN(widthFeet) || isNaN(heightFeet) || widthFeet <= 0 || heightFeet <= 0) {
        console.warn('Invalid dimension conversion:', { width, height, widthFeet, heightFeet });
        return 0;
      }

      // Calculate area with validation
      const area = widthFeet * heightFeet;

      // Final validation - allow very small values but not zero
      if (isNaN(area) || area <= 0) {
        console.warn('Invalid area calculation result:', { widthFeet, heightFeet, area });
        return 0;
      }

      return area;
    } else {
      console.warn('Invalid dimension format:', { width, height });
      return 0;
    }
  } catch (error) {
    console.error('Error calculating area:', error);
    return 0;
  }
};

/**
 * Formats a dimension for display
 * @param dimension The dimension to format (can be Dimension object or number)
 * @returns Formatted dimension string
 */
export const formatDimension = (dimension: Dimension | number): string => {
  try {
    // Handle different dimension formats
    if (typeof dimension === 'number') {
      // Convert numeric dimension (assumed to be in inches) to fractional format
      return `${decimalToFraction(dimension)}"`;
    } else if (typeof dimension === 'object' && dimension !== null) {
      // Validate and format Dimension object
      try {
        validateDimension(dimension);
        const fractionStr = dimension.fraction !== '0' ? ` ${dimension.fraction}` : '';
        return `${dimension.inches}${fractionStr}"`;
      } catch (validationError) {
        console.warn('Dimension validation failed, using basic formatting:', validationError);
        // Basic formatting if validation fails
        const inches = dimension.inches || 0;
        const fraction = dimension.fraction || '0';
        const fractionStr = fraction !== '0' ? ` ${fraction}` : '';
        return `${inches}${fractionStr}"`;
      }
    } else {
      console.warn('Invalid dimension format:', dimension);
      return '0"';
    }
  } catch (error) {
    console.error('Error formatting dimension:', error);
    return '0"';
  }
};

/**
 * Creates a default dimension object
 */
export const createDefaultDimension = (): Dimension => ({
  feet: 0,
  inches: 0,
  fraction: '0'
});

/**
 * Creates an empty dimension object
 */
export const createEmptyDimension = (): Dimension => ({
  feet: 0,
  inches: 0,
  fraction: '0'
});

/**
 * Converts a decimal value to a dimension object
 * @param decimal The decimal value to convert
 * @returns A dimension object
 */
export const decimalToDimension = (decimal: number): Dimension => {
  try {
    if (typeof decimal !== 'number' || isNaN(decimal)) {
      throw new Error('Invalid decimal value');
    }

    const totalInches = decimal * MEASUREMENT_CONSTANTS.INCHES_PER_FOOT;
    const wholeInches = Math.floor(totalInches);
    const decimalPart = totalInches - wholeInches;

    // Find the closest fraction using pre-calculated decimals
    const closestFraction = fractionOptions.reduce((prev, curr) => {
      const prevDiff = Math.abs(decimalPart - prev.decimal);
      const currDiff = Math.abs(decimalPart - curr.decimal);
      return currDiff < prevDiff ? curr : prev;
    }).value;

    return {
      feet: 0,
      inches: wholeInches,
      fraction: closestFraction,
    };
  } catch (error) {
    console.error('Error converting decimal to dimension:', error);
    return createDefaultDimension();
  }
};

/**
 * Converts a decimal inch value to a fractional representation with 1/16 precision
 * @param inches The decimal inch value to convert
 * @returns A string with the whole number and fraction (e.g., "5 3/16")
 */
export const decimalToFraction = (inches: number): string => {
  try {
    if (typeof inches !== 'number' || isNaN(inches)) {
      return '0';
    }

    // Extract the whole number part
    const wholeNumber = Math.floor(inches);

    // Get the decimal part
    const decimalPart = inches - wholeNumber;

    // Convert to 16ths
    const sixteenths = Math.round(decimalPart * 16);

    // Handle case where rounding gives us 16/16
    if (sixteenths === 16) {
      return `${wholeNumber + 1}`;
    }

    // If there's no fractional part, just return the whole number
    if (sixteenths === 0) {
      return `${wholeNumber}`;
    }

    // Reduce the fraction if possible
    let numerator = sixteenths;
    let denominator = 16;

    // Find the greatest common divisor
    const gcd = (a: number, b: number): number => {
      return b === 0 ? a : gcd(b, a % b);
    };

    const divisor = gcd(numerator, denominator);
    numerator = numerator / divisor;
    denominator = denominator / divisor;

    // Return the formatted string
    if (wholeNumber === 0) {
      return `${numerator}/${denominator}`;
    } else {
      return `${wholeNumber} ${numerator}/${denominator}`;
    }
  } catch (error) {
    console.error('Error converting decimal to fraction:', error);
    return '0';
  }
};

/**
 * Formats measurement options for display
 * @param options The measurement options to format
 * @returns Formatted options string
 */
export const getMeasurementOptionsText = (options: MeasurementOptions | null): string => {
  try {
    if (!options) return "No measurement options specified";

    const parts = [
      `Glass Thickness: ${options.thickness}`,
      `Measurement Type: ${options.measurementType}`,
      `System: ${options.system}`
    ];

    return parts.join(', ');
  } catch (error) {
    console.error('Error formatting measurement options:', error);
    return "Error formatting measurement options";
  }
};
