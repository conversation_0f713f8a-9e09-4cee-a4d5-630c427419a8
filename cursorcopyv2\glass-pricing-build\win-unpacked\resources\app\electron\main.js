const { app, BrowserWindow, ipcMain, dialog, shell, Menu } = require('electron');
const path = require('path');
const fs = require('fs');
const { exec } = require('child_process');
const { setupFolders } = require('./services/fileSystem');
const { migrateData } = require('./services/dataManager');
const { checkForUpdates } = require('./services/updater');
const { registerFileHandlers } = require('./services/fileHandlers');
const isDev = process.env.NODE_ENV === 'development';

// Check if Windows Developer Mode is enabled
const checkWindowsDeveloperMode = () => {
  return new Promise((resolve) => {
    // Only check on Windows
    if (process.platform !== 'win32') {
      resolve(true);
      return;
    }

    // Use PowerShell to check the registry
    exec('powershell -command "Get-ItemProperty -Path HKLM:\\SOFTWARE\\Microsoft\\Windows\\CurrentVersion\\AppModelUnlock -Name AllowDevelopmentWithoutDevLicense -ErrorAction SilentlyContinue | Select-Object -ExpandProperty AllowDevelopmentWithoutDevLicense"', (error, stdout) => {
      if (error) {
        console.error('Error checking Developer Mode:', error);
        resolve(false);
        return;
      }

      // Parse the output
      const value = parseInt(stdout.trim());
      const isDeveloperModeEnabled = value === 1;

      console.log(`Windows Developer Mode is ${isDeveloperModeEnabled ? 'enabled' : 'disabled'}`);
      resolve(isDeveloperModeEnabled);
    });
  });
};

// Keep a global reference of the window object to prevent garbage collection
let mainWindow;

async function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, 'assets', 'icon.png'),
    show: false // Don't show until ready-to-show
  });

  // Set up application menu
  const menu = Menu.buildFromTemplate([
    {
      label: 'File',
      submenu: [
        {
          label: 'Open Price Lists Folder',
          click: () => shell.openPath(app.getPath('documents') + '/Glass Pricing/Price Lists')
        },
        {
          label: 'Open Estimates Folder',
          click: () => shell.openPath(app.getPath('documents') + '/Glass Pricing/Estimates')
        },
        { type: 'separator' },
        { role: 'quit' }
      ]
    },
    {
      label: 'Edit',
      submenu: [
        { role: 'undo' },
        { role: 'redo' },
        { type: 'separator' },
        { role: 'cut' },
        { role: 'copy' },
        { role: 'paste' }
      ]
    },
    {
      label: 'View',
      submenu: [
        { role: 'reload' },
        { role: 'forceReload' },
        { type: 'separator' },
        { role: 'resetZoom' },
        { role: 'zoomIn' },
        { role: 'zoomOut' },
        { type: 'separator' },
        { role: 'togglefullscreen' }
      ]
    },
    {
      label: 'Help',
      submenu: [
        {
          label: 'Check for Updates',
          click: async () => {
            const updateAvailable = await checkForUpdates(true);
            if (!updateAvailable) {
              dialog.showMessageBox(mainWindow, {
                type: 'info',
                title: 'Updates',
                message: 'You are using the latest version.'
              });
            }
          }
        },
        {
          label: 'About',
          click: () => {
            dialog.showMessageBox(mainWindow, {
              type: 'info',
              title: 'About Glass Pricing',
              message: `Glass Pricing v${app.getVersion()}`,
              detail: 'A desktop application for glass pricing and estimates.'
            });
          }
        }
      ]
    }
  ]);
  Menu.setApplicationMenu(menu);

  // Load the app
  if (isDev) {
    // In development, load from development server
    await mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    // In production, load from built files
    // Try different possible paths for the index.html file
    try {
      await mainWindow.loadFile(path.join(__dirname, '../index.html')).catch(error => { console.error('Failed to load index.html:', error); });
    } catch (error) {
      console.error('Failed to load from ../dist/index.html:', error);
      try {
        await mainWindow.loadFile(path.join(__dirname, '../build/index.html'));
      } catch (error) {
        console.error('Failed to load from ../build/index.html:', error);
        try {
          await mainWindow.loadFile(path.join(__dirname, '../../dist/index.html'));
        } catch (error) {
          console.error('Failed to load from ../../dist/index.html:', error);
          // As a last resort, try to find the index.html file
          const possiblePaths = [
            path.join(__dirname, '../index.html'),
            path.join(__dirname, '../../index.html'),
            path.join(__dirname, '../../../index.html'),
            path.join(app.getAppPath(), 'dist/index.html'),
            path.join(app.getAppPath(), 'index.html')
          ];

          let loaded = false;
          for (const possiblePath of possiblePaths) {
            try {
              if (fs.existsSync(possiblePath)) {
                console.log(`Found index.html at ${possiblePath}`);
                await mainWindow.loadFile(possiblePath);
                loaded = true;
                break;
              }
            } catch (e) {
              console.error(`Failed to load from ${possiblePath}:`, e);
            }
          }

          if (!loaded) {
            throw new Error('Could not find index.html file');
          }
        }
      }
    }
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window close
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// Initialize the app
app.whenReady().then(async () => {
  try {
    console.log('Starting application initialization...');

    // Check for Windows Developer Mode if on Windows
    if (process.platform === 'win32') {
      console.log('Checking Windows Developer Mode status...');
      const isDeveloperModeEnabled = await checkWindowsDeveloperMode();

      if (!isDeveloperModeEnabled) {
        console.warn('Windows Developer Mode is not enabled. This may cause issues with file operations.');

        // Show a warning dialog but continue with initialization
        if (!isDev) { // Only show in production
          dialog.showMessageBox({
            type: 'warning',
            title: 'Developer Mode Not Enabled',
            message: 'Windows Developer Mode is not enabled',
            detail: 'Some features may not work correctly. If you encounter issues, please enable Developer Mode in Windows Settings > Update & Security > For developers.',
            buttons: ['Continue Anyway'],
            defaultId: 0
          });
        }
      }
    }

    // Set up required folders
    console.log('Setting up application folders...');
    await setupFolders();

    // Register IPC handlers for file operations
    console.log('Registering IPC handlers...');
    registerFileHandlers(ipcMain);

    // Create the main window
    console.log('Creating main window...');
    await createWindow();

    // Check for first run and migrate data if needed
    console.log('Checking for first run...');
    const isFirstRun = await migrateData(mainWindow);

    // Check for updates (but not on first run)
    if (!isFirstRun && !isDev) {
      console.log('Scheduling update check...');
      setTimeout(() => checkForUpdates(false), 5000);
    }

    console.log('Application initialization complete!');
  } catch (error) {
    console.error('Error during app initialization:', error);

    // Check for Windows-specific errors
    let errorMessage = `There was an error initializing the application: ${error.message}`;

    // Check for common Windows symbolic link errors
    if (error.message.includes('symlink') || error.message.includes('EPERM')) {
      errorMessage = 'Permission error: The application may need to be run as administrator or with Developer Mode enabled.\n\n' +
                    'To enable Developer Mode on Windows 10/11:\n' +
                    '1. Open Settings > Update & Security > For developers\n' +
                    '2. Enable "Developer Mode"\n\n' +
                    'Or try running the application as administrator.\n\n' +
                    `Original error: ${error.message}`;
    }

    dialog.showErrorBox('Initialization Error', errorMessage);
  }
});

// Quit when all windows are closed, except on macOS
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// On macOS, re-create window when dock icon is clicked
app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

// Handle any uncaught exceptions
process.on('uncaughtException', (error) => {
  console.error('Uncaught exception:', error);
  dialog.showErrorBox(
    'Unexpected Error',
    `An unexpected error occurred: ${error.message}`
  );
});
