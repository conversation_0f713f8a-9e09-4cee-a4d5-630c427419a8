import React, { createContext, useContext, useState, useEffect } from 'react';
import { generateUUID } from '@/utils/uuid';
import { storageService } from '@/services/storageService';
import { toast } from '@/components/ui/use-toast';
import { MeasurementOptions } from '@/components/MeasurementToggleOptions';
import { GlassTypeCache } from '@/services/GlassTypeCache';

export type ThicknessOption =
  | "1/4"
  | "3/8"
  | "1/2"
  | "9/16"
  | "3/4"
  | "1"
  | "1 5/16";

export type GlassTypeOption = 'single' | 'laminate' | 'insulated';

export type GlassType = {
  id: string;
  name: string;
  thickness: ThicknessOption;
  costPerSqFt: number;
  weightPerSqFt: number;
  type: GlassTypeOption;
  isRestored?: boolean;
  category?: string;
};

export type Dimension = {
  feet: number;
  inches: number;
  fraction: string;
};

export type EstimateItem = {
  id: string;
  width: Dimension | number;
  height: Dimension | number;
  glassType: string | GlassType;
  quantity: number;
  thickness: ThicknessOption;
  measurementOptionsId: string;
  measurementType?: 'DLO' | 'GS';
  frameType?: string;
  curtainWallType?: string;
  adjustedWidth?: number;
  adjustedHeight?: number;
};

export type MeasurementOptionsSet = {
  id: string;
  name: string;
  thickness: ThicknessOption | 'all';
  measurementType: 'DLO' | 'GS';
  system: 'Storefront' | 'Curtainwall' | 'SSG' | 'Comm. Insert' | 'Res.Insert' | 'Mirror' | 'Residential IG';
  allGlassSameMake: boolean;
  storefrontDLOAddition?: '1/2' | '5/8'; // New option for 1/4" glass in storefront systems
  items: EstimateItem[];
};

export type EstimateSettings = {
  taxRate: number;
  surchargeRate: number;
  markupRate: number;
  shippingRate: number;
  otherFee: number;
};

export type Estimate = {
  id: string;
  name: string;
  items: EstimateItem[];
  created: Date;
  companyName: string;
  measurementOptionsSets: MeasurementOptionsSet[]; // New array to hold multiple sets
  settings?: EstimateSettings; // Optional settings for the estimate
};

export type CompanyInfo = {
  name: string;
  address: string;
  phone: string;
  email: string;
  useManufacturerRounding: boolean;
};

interface GlassContextType {
  glassTypes: GlassType[];
  addGlassType: (glassType: Omit<GlassType, 'id'>) => void;
  updateGlassType: (id: string, glassType: Partial<GlassType>) => void;
  deleteGlassType: (id: string) => void;
  resetGlassTypes: () => void;
  estimates: Estimate[];
  currentEstimate: Estimate | null;
  setCurrentEstimate: (estimate: Estimate | null) => void;
  saveEstimate: (estimate: Omit<Estimate, 'id' | 'created'>) => void;
  deleteEstimate: (id: string) => void;
  companyInfo: CompanyInfo;
  updateCompanyInfo: (info: Partial<CompanyInfo>) => void;
  getAdjustedArea: (width: number, height: number) => number;
  findGlassTypeById: (id: string) => GlassType | undefined;
}

const defaultCompanyInfo: CompanyInfo = {
  name: 'ServicePane',
  address: '123 Glass Ave, Suite 100, Anytown, USA 12345',
  phone: '(*************',
  email: '<EMAIL>',
  useManufacturerRounding: false,
};

const GlassContext = createContext<GlassContextType | undefined>(undefined);

// Helper function to convert thickness number to string
const convertThicknessToString = (thickness: number): ThicknessOption => {
  if (thickness === 0.25) return "1/4";
  if (thickness === 0.375) return "3/8";
  if (thickness === 0.5) return "1/2";
  if (thickness === 0.5625) return "9/16";
  if (thickness === 0.75) return "3/4";
  if (thickness === 1) return "1";
  if (thickness === 1.3125) return "1 5/16";
  // Default fallback
  return "1/4";
};

// Helper function to convert glass type string to enum
const convertGlassTypeToEnum = (type: string): GlassTypeOption => {
  const lowerType = type.toLowerCase();
  if (lowerType.includes('laminated') || lowerType.includes('lami')) return 'laminate';
  if (lowerType.includes('ig') || lowerType.includes('insulated')) return 'insulated';
  return 'single';
};

// Comprehensive default glass types
export const defaultGlassTypes: GlassType[] = [
  // Mirror types
  {
    id: '1',
    name: 'Clear Mirror',
    thickness: "1/4",
    costPerSqFt: 7.28,
    weightPerSqFt: 3.24,
    type: 'single',
    category: 'Mirror'
  },
  {
    id: '2',
    name: 'Bronze Mirror',
    thickness: "1/4",
    costPerSqFt: 17.49,
    weightPerSqFt: 3.24,
    type: 'single',
    category: 'Mirror'
  },
  {
    id: '3',
    name: 'Grey Mirror',
    thickness: "1/4",
    costPerSqFt: 17.49,
    weightPerSqFt: 3.24,
    type: 'single',
    category: 'Mirror'
  },
  {
    id: '4',
    name: 'Clear Mirror',
    thickness: "1/8",
    costPerSqFt: 7.29,
    weightPerSqFt: 1.62,
    type: 'single',
    category: 'Mirror'
  },
  {
    id: '5',
    name: 'Clear Mirror',
    thickness: "3/16",
    costPerSqFt: 7.28,
    weightPerSqFt: 2.43,
    type: 'single',
    category: 'Mirror'
  },
  {
    id: '6',
    name: 'Antique Mirror',
    thickness: "1/4",
    costPerSqFt: 40.07,
    weightPerSqFt: 3.24,
    type: 'single',
    category: 'Mirror'
  },
  // Monolithic types
  {
    id: '7',
    name: 'SSB Clear',
    thickness: "1/8",
    costPerSqFt: 1.89,
    weightPerSqFt: 0.81,
    type: 'single',
    category: 'Monolithic'
  },
  {
    id: '8',
    name: 'Clear',
    thickness: "1/8",
    costPerSqFt: 3.25,
    weightPerSqFt: 1.62,
    type: 'single',
    category: 'Monolithic'
  },
  {
    id: '9',
    name: 'Clear',
    thickness: "1/4",
    costPerSqFt: 3.25,
    weightPerSqFt: 3.24,
    type: 'single',
    category: 'Monolithic'
  },
  {
    id: '10',
    name: 'Bronze',
    thickness: "1/4",
    costPerSqFt: 5.64,
    weightPerSqFt: 3.24,
    type: 'single',
    category: 'Monolithic'
  },
  {
    id: '11',
    name: 'Grey',
    thickness: "1/4",
    costPerSqFt: 5.64,
    weightPerSqFt: 3.24,
    type: 'single',
    category: 'Monolithic'
  },
  {
    id: '12',
    name: 'Green',
    thickness: "1/4",
    costPerSqFt: 5.64,
    weightPerSqFt: 3.24,
    type: 'single',
    category: 'Monolithic'
  },
  {
    id: '13',
    name: 'Clear',
    thickness: "3/8",
    costPerSqFt: 13.97,
    weightPerSqFt: 4.86,
    type: 'single',
    category: 'Monolithic'
  },
  {
    id: '14',
    name: 'Clear',
    thickness: "1/2",
    costPerSqFt: 16.30,
    weightPerSqFt: 6.48,
    type: 'single',
    category: 'Monolithic'
  },
  // Laminated types
  {
    id: '15',
    name: 'Clear Lami',
    thickness: "1/4",
    costPerSqFt: 14.59,
    weightPerSqFt: 3.40,
    type: 'laminate',
    category: 'Laminated'
  },
  {
    id: '16',
    name: 'Clear Lami',
    thickness: "3/8",
    costPerSqFt: 18.95,
    weightPerSqFt: 5.10,
    type: 'laminate',
    category: 'Laminated'
  },
  {
    id: '17',
    name: 'Clear Lami',
    thickness: "1/2",
    costPerSqFt: 23.31,
    weightPerSqFt: 6.80,
    type: 'laminate',
    category: 'Laminated'
  },
  // IG Commercial types
  {
    id: '18',
    name: 'Clear over Clear',
    thickness: "1",
    costPerSqFt: 11.63,
    weightPerSqFt: 6.50,
    type: 'insulated',
    category: 'IG Commercial'
  },
  {
    id: '19',
    name: 'Low-E over Clear',
    thickness: "1",
    costPerSqFt: 13.63,
    weightPerSqFt: 6.50,
    type: 'insulated',
    category: 'IG Commercial'
  },
  {
    id: '20',
    name: 'Clear over Low-E',
    thickness: "1",
    costPerSqFt: 13.63,
    weightPerSqFt: 6.50,
    type: 'insulated',
    category: 'IG Commercial'
  },
  // IG Residential types
  {
    id: '21',
    name: 'Clear over Clear',
    thickness: "1 5/16",
    costPerSqFt: 12.63,
    weightPerSqFt: 6.50,
    type: 'insulated',
    category: 'IG Residential'
  },
  {
    id: '22',
    name: 'Low-E over Clear',
    thickness: "1 5/16",
    costPerSqFt: 14.63,
    weightPerSqFt: 6.50,
    type: 'insulated',
    category: 'IG Residential'
  }
];

export const GlassProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [glassTypes, setGlassTypes] = useState<GlassType[]>([]);
  const [estimates, setEstimates] = useState<Estimate[]>([]);
  const [currentEstimate, setCurrentEstimate] = useState<Estimate | null>(null);
  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>(defaultCompanyInfo);
  const [dataLoaded, setDataLoaded] = useState<boolean>(false);

  // Load data from storage when component mounts
  useEffect(() => {
    const loadData = async () => {
      try {
        const [storedGlassTypes, storedEstimates, storedCompanyInfo] = await Promise.all([
          storageService.getData('glassTypes', defaultGlassTypes),
          storageService.getData('estimates', []),
          storageService.getData('companyInfo', defaultCompanyInfo)
        ]);

        // Clear the glass type cache when loading new data
        GlassTypeCache.clearCache();

        setGlassTypes(storedGlassTypes);
        setEstimates(storedEstimates);
        setCompanyInfo(storedCompanyInfo);
        setDataLoaded(true);
      } catch (error) {
        toast({
          title: "Error loading data",
          description: "Failed to load application data. Some features may not work correctly.",
          variant: "destructive"
        });
      }
    };

    loadData();
  }, []);

  // Save data when it changes
  useEffect(() => {
    const saveData = async () => {
      if (!dataLoaded) return;

      try {
        await Promise.all([
          storageService.setData('glassTypes', glassTypes),
          storageService.setData('estimates', estimates),
          storageService.setData('companyInfo', companyInfo)
        ]);
      } catch (error) {
        toast({
          title: "Error saving data",
          description: "Failed to save changes. Please try again.",
          variant: "destructive"
        });
      }
    };

    saveData();
  }, [glassTypes, estimates, companyInfo, dataLoaded]);

  const generateId = () => {
    // Generate a more reliable ID using timestamp and random number
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  };

  const addGlassType = (glassType: Omit<GlassType, 'id'>) => {
    const newGlassType = {
      ...glassType,
      id: generateId(),
    };
    setGlassTypes([...glassTypes, newGlassType]);
  };

  const updateGlassType = (id: string, updatedFields: Partial<GlassType>) => {
    setGlassTypes(
      glassTypes.map((type) => (type.id === id ? { ...type, ...updatedFields } : type))
    );
  };

  const deleteGlassType = (id: string) => {
    const updatedTypes = glassTypes.filter((type) => type.id !== id);
    // If this would delete the last glass type, add a blank one
    if (updatedTypes.length === 0) {
      const blankType: GlassType = {
        id: generateId(),
        name: '',
        costPerSqFt: 0,
        weightPerSqFt: 3.24,
        thickness: '1/4',
        type: 'single'
      };
      setGlassTypes([blankType]);
    } else {
      setGlassTypes(updatedTypes);
    }
  };

  const resetGlassTypes = () => {
    setGlassTypes(defaultGlassTypes);
    toast({
      title: "Glass types reset",
      description: "Glass types have been restored to the default comprehensive list.",
    });
  };

  const saveEstimate = (estimate: Omit<Estimate, 'id' | 'created'>) => {
    const newEstimate = {
      ...estimate,
      id: currentEstimate?.id || generateId(),
      created: currentEstimate?.created || new Date(),
    };

    if (currentEstimate) {
      setEstimates(
        estimates.map((est) => (est.id === currentEstimate.id ? newEstimate : est))
      );
    } else {
      setEstimates([...estimates, newEstimate]);
    }

    setCurrentEstimate(null);
  };

  const deleteEstimate = (id: string) => {
    setEstimates(estimates.filter((est) => est.id !== id));
    if (currentEstimate?.id === id) {
      setCurrentEstimate(null);
    }
  };

  const updateCompanyInfo = (info: Partial<CompanyInfo>) => {
    setCompanyInfo({ ...companyInfo, ...info });
  };

  const defaultMeasurementOptionsSet: MeasurementOptionsSet = {
    id: generateUUID(),
    name: "Default Options",
    thickness: 'all',
    measurementType: 'DLO',
    system: 'Storefront',
    allGlassSameMake: false,
    storefrontDLOAddition: '1/2', // Default to 1/2" addition
    items: []
  };

  const defaultEstimateItem: EstimateItem = {
    id: '',
    width: { feet: 0, inches: 0, fraction: '0' },
    height: { feet: 0, inches: 0, fraction: '0' },
    glassType: '',
    quantity: 1,
    thickness: "1/4",
    measurementOptionsId: '',
  };

  const getAdjustedArea = (width: number, height: number): number => {
    if (!companyInfo.useManufacturerRounding) return (width * height) / 144;

    // Apply manufacturer rounding logic
    const roundedWidth = Math.ceil(width) + 1;
    const roundedHeight = Math.ceil(height) + 1;
    return (roundedWidth * roundedHeight) / 144;
  };

  // Find a glass type by ID, with fallback to cache/restoration
  const findGlassTypeById = (id: string): GlassType | undefined => {
    // First check in the regular glass types
    const regularType = glassTypes.find((type) => type.id === id);
    if (regularType) return regularType;

    // If not found, try to get it from the cache or restore it
    return GlassTypeCache.getOrRestoreGlassType(id, glassTypes);
  };

  return (
    <GlassContext.Provider
      value={{
        glassTypes,
        addGlassType,
        updateGlassType,
        deleteGlassType,
        resetGlassTypes,
        estimates,
        currentEstimate,
        setCurrentEstimate,
        saveEstimate,
        deleteEstimate,
        companyInfo,
        updateCompanyInfo,
        getAdjustedArea,
        findGlassTypeById,
      }}
    >
      {children}
    </GlassContext.Provider>
  );
};

export const useGlassContext = () => {
  const context = useContext(GlassContext);
  if (context === undefined) {
    throw new Error('useGlassContext must be used within a GlassProvider');
  }
  return context;
};
