{"version": 3, "sources": ["../../csv-parse/dist/esm/sync.js"], "sourcesContent": ["var global$1 = (typeof global !== \"undefined\" ? global :\n            typeof self !== \"undefined\" ? self :\n            typeof window !== \"undefined\" ? window : {});\n\nvar lookup = [];\nvar revLookup = [];\nvar Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array;\nvar inited = false;\nfunction init () {\n  inited = true;\n  var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/';\n  for (var i = 0, len = code.length; i < len; ++i) {\n    lookup[i] = code[i];\n    revLookup[code.charCodeAt(i)] = i;\n  }\n\n  revLookup['-'.charCodeAt(0)] = 62;\n  revLookup['_'.charCodeAt(0)] = 63;\n}\n\nfunction toByteArray (b64) {\n  if (!inited) {\n    init();\n  }\n  var i, j, l, tmp, placeHolders, arr;\n  var len = b64.length;\n\n  if (len % 4 > 0) {\n    throw new Error('Invalid string. Length must be a multiple of 4')\n  }\n\n  // the number of equal signs (place holders)\n  // if there are two placeholders, than the two characters before it\n  // represent one byte\n  // if there is only one, then the three characters before it represent 2 bytes\n  // this is just a cheap hack to not do indexOf twice\n  placeHolders = b64[len - 2] === '=' ? 2 : b64[len - 1] === '=' ? 1 : 0;\n\n  // base64 is 4/3 + up to two characters of the original data\n  arr = new Arr(len * 3 / 4 - placeHolders);\n\n  // if there are placeholders, only get up to the last complete 4 chars\n  l = placeHolders > 0 ? len - 4 : len;\n\n  var L = 0;\n\n  for (i = 0, j = 0; i < l; i += 4, j += 3) {\n    tmp = (revLookup[b64.charCodeAt(i)] << 18) | (revLookup[b64.charCodeAt(i + 1)] << 12) | (revLookup[b64.charCodeAt(i + 2)] << 6) | revLookup[b64.charCodeAt(i + 3)];\n    arr[L++] = (tmp >> 16) & 0xFF;\n    arr[L++] = (tmp >> 8) & 0xFF;\n    arr[L++] = tmp & 0xFF;\n  }\n\n  if (placeHolders === 2) {\n    tmp = (revLookup[b64.charCodeAt(i)] << 2) | (revLookup[b64.charCodeAt(i + 1)] >> 4);\n    arr[L++] = tmp & 0xFF;\n  } else if (placeHolders === 1) {\n    tmp = (revLookup[b64.charCodeAt(i)] << 10) | (revLookup[b64.charCodeAt(i + 1)] << 4) | (revLookup[b64.charCodeAt(i + 2)] >> 2);\n    arr[L++] = (tmp >> 8) & 0xFF;\n    arr[L++] = tmp & 0xFF;\n  }\n\n  return arr\n}\n\nfunction tripletToBase64 (num) {\n  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F]\n}\n\nfunction encodeChunk (uint8, start, end) {\n  var tmp;\n  var output = [];\n  for (var i = start; i < end; i += 3) {\n    tmp = (uint8[i] << 16) + (uint8[i + 1] << 8) + (uint8[i + 2]);\n    output.push(tripletToBase64(tmp));\n  }\n  return output.join('')\n}\n\nfunction fromByteArray (uint8) {\n  if (!inited) {\n    init();\n  }\n  var tmp;\n  var len = uint8.length;\n  var extraBytes = len % 3; // if we have 1 byte left, pad 2 bytes\n  var output = '';\n  var parts = [];\n  var maxChunkLength = 16383; // must be multiple of 3\n\n  // go through the array every three bytes, we'll deal with trailing stuff later\n  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {\n    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)));\n  }\n\n  // pad the end with zeros, but make sure to not forget the extra bytes\n  if (extraBytes === 1) {\n    tmp = uint8[len - 1];\n    output += lookup[tmp >> 2];\n    output += lookup[(tmp << 4) & 0x3F];\n    output += '==';\n  } else if (extraBytes === 2) {\n    tmp = (uint8[len - 2] << 8) + (uint8[len - 1]);\n    output += lookup[tmp >> 10];\n    output += lookup[(tmp >> 4) & 0x3F];\n    output += lookup[(tmp << 2) & 0x3F];\n    output += '=';\n  }\n\n  parts.push(output);\n\n  return parts.join('')\n}\n\nfunction read (buffer, offset, isLE, mLen, nBytes) {\n  var e, m;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var nBits = -7;\n  var i = isLE ? (nBytes - 1) : 0;\n  var d = isLE ? -1 : 1;\n  var s = buffer[offset + i];\n\n  i += d;\n\n  e = s & ((1 << (-nBits)) - 1);\n  s >>= (-nBits);\n  nBits += eLen;\n  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  m = e & ((1 << (-nBits)) - 1);\n  e >>= (-nBits);\n  nBits += mLen;\n  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}\n\n  if (e === 0) {\n    e = 1 - eBias;\n  } else if (e === eMax) {\n    return m ? NaN : ((s ? -1 : 1) * Infinity)\n  } else {\n    m = m + Math.pow(2, mLen);\n    e = e - eBias;\n  }\n  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)\n}\n\nfunction write (buffer, value, offset, isLE, mLen, nBytes) {\n  var e, m, c;\n  var eLen = nBytes * 8 - mLen - 1;\n  var eMax = (1 << eLen) - 1;\n  var eBias = eMax >> 1;\n  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0);\n  var i = isLE ? 0 : (nBytes - 1);\n  var d = isLE ? 1 : -1;\n  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0;\n\n  value = Math.abs(value);\n\n  if (isNaN(value) || value === Infinity) {\n    m = isNaN(value) ? 1 : 0;\n    e = eMax;\n  } else {\n    e = Math.floor(Math.log(value) / Math.LN2);\n    if (value * (c = Math.pow(2, -e)) < 1) {\n      e--;\n      c *= 2;\n    }\n    if (e + eBias >= 1) {\n      value += rt / c;\n    } else {\n      value += rt * Math.pow(2, 1 - eBias);\n    }\n    if (value * c >= 2) {\n      e++;\n      c /= 2;\n    }\n\n    if (e + eBias >= eMax) {\n      m = 0;\n      e = eMax;\n    } else if (e + eBias >= 1) {\n      m = (value * c - 1) * Math.pow(2, mLen);\n      e = e + eBias;\n    } else {\n      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen);\n      e = 0;\n    }\n  }\n\n  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}\n\n  e = (e << mLen) | m;\n  eLen += mLen;\n  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}\n\n  buffer[offset + i - d] |= s * 128;\n}\n\nvar toString = {}.toString;\n\nvar isArray = Array.isArray || function (arr) {\n  return toString.call(arr) == '[object Array]';\n};\n\nvar INSPECT_MAX_BYTES = 50;\n\n/**\n * If `Buffer.TYPED_ARRAY_SUPPORT`:\n *   === true    Use Uint8Array implementation (fastest)\n *   === false   Use Object implementation (most compatible, even IE6)\n *\n * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,\n * Opera 11.6+, iOS 4.2+.\n *\n * Due to various browser bugs, sometimes the Object implementation will be used even\n * when the browser supports typed arrays.\n *\n * Note:\n *\n *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,\n *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.\n *\n *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.\n *\n *   - IE10 has a broken `TypedArray.prototype.subarray` function which returns arrays of\n *     incorrect length in some situations.\n\n * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they\n * get the Object implementation, which is slower but behaves correctly.\n */\nBuffer.TYPED_ARRAY_SUPPORT = global$1.TYPED_ARRAY_SUPPORT !== undefined\n  ? global$1.TYPED_ARRAY_SUPPORT\n  : true;\n\n/*\n * Export kMaxLength after typed array support is determined.\n */\nkMaxLength();\n\nfunction kMaxLength () {\n  return Buffer.TYPED_ARRAY_SUPPORT\n    ? 0x7fffffff\n    : 0x3fffffff\n}\n\nfunction createBuffer (that, length) {\n  if (kMaxLength() < length) {\n    throw new RangeError('Invalid typed array length')\n  }\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = new Uint8Array(length);\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    if (that === null) {\n      that = new Buffer(length);\n    }\n    that.length = length;\n  }\n\n  return that\n}\n\n/**\n * The Buffer constructor returns instances of `Uint8Array` that have their\n * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of\n * `Uint8Array`, so the returned instances will have all the node `Buffer` methods\n * and the `Uint8Array` methods. Square bracket notation works as expected -- it\n * returns a single octet.\n *\n * The `Uint8Array` prototype remains unmodified.\n */\n\nfunction Buffer (arg, encodingOrOffset, length) {\n  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {\n    return new Buffer(arg, encodingOrOffset, length)\n  }\n\n  // Common case.\n  if (typeof arg === 'number') {\n    if (typeof encodingOrOffset === 'string') {\n      throw new Error(\n        'If encoding is specified then the first argument must be a string'\n      )\n    }\n    return allocUnsafe(this, arg)\n  }\n  return from(this, arg, encodingOrOffset, length)\n}\n\nBuffer.poolSize = 8192; // not used by this implementation\n\n// TODO: Legacy, not needed anymore. Remove in next major version.\nBuffer._augment = function (arr) {\n  arr.__proto__ = Buffer.prototype;\n  return arr\n};\n\nfunction from (that, value, encodingOrOffset, length) {\n  if (typeof value === 'number') {\n    throw new TypeError('\"value\" argument must not be a number')\n  }\n\n  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {\n    return fromArrayBuffer(that, value, encodingOrOffset, length)\n  }\n\n  if (typeof value === 'string') {\n    return fromString(that, value, encodingOrOffset)\n  }\n\n  return fromObject(that, value)\n}\n\n/**\n * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError\n * if value is a number.\n * Buffer.from(str[, encoding])\n * Buffer.from(array)\n * Buffer.from(buffer)\n * Buffer.from(arrayBuffer[, byteOffset[, length]])\n **/\nBuffer.from = function (value, encodingOrOffset, length) {\n  return from(null, value, encodingOrOffset, length)\n};\n\nif (Buffer.TYPED_ARRAY_SUPPORT) {\n  Buffer.prototype.__proto__ = Uint8Array.prototype;\n  Buffer.__proto__ = Uint8Array;\n  if (typeof Symbol !== 'undefined' && Symbol.species &&\n      Buffer[Symbol.species] === Buffer) ;\n}\n\nfunction assertSize (size) {\n  if (typeof size !== 'number') {\n    throw new TypeError('\"size\" argument must be a number')\n  } else if (size < 0) {\n    throw new RangeError('\"size\" argument must not be negative')\n  }\n}\n\nfunction alloc (that, size, fill, encoding) {\n  assertSize(size);\n  if (size <= 0) {\n    return createBuffer(that, size)\n  }\n  if (fill !== undefined) {\n    // Only pay attention to encoding if it's a string. This\n    // prevents accidentally sending in a number that would\n    // be interpretted as a start offset.\n    return typeof encoding === 'string'\n      ? createBuffer(that, size).fill(fill, encoding)\n      : createBuffer(that, size).fill(fill)\n  }\n  return createBuffer(that, size)\n}\n\n/**\n * Creates a new filled Buffer instance.\n * alloc(size[, fill[, encoding]])\n **/\nBuffer.alloc = function (size, fill, encoding) {\n  return alloc(null, size, fill, encoding)\n};\n\nfunction allocUnsafe (that, size) {\n  assertSize(size);\n  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) {\n    for (var i = 0; i < size; ++i) {\n      that[i] = 0;\n    }\n  }\n  return that\n}\n\n/**\n * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.\n * */\nBuffer.allocUnsafe = function (size) {\n  return allocUnsafe(null, size)\n};\n/**\n * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.\n */\nBuffer.allocUnsafeSlow = function (size) {\n  return allocUnsafe(null, size)\n};\n\nfunction fromString (that, string, encoding) {\n  if (typeof encoding !== 'string' || encoding === '') {\n    encoding = 'utf8';\n  }\n\n  if (!Buffer.isEncoding(encoding)) {\n    throw new TypeError('\"encoding\" must be a valid string encoding')\n  }\n\n  var length = byteLength(string, encoding) | 0;\n  that = createBuffer(that, length);\n\n  var actual = that.write(string, encoding);\n\n  if (actual !== length) {\n    // Writing a hex string, for example, that contains invalid characters will\n    // cause everything after the first invalid character to be ignored. (e.g.\n    // 'abxxcd' will be treated as 'ab')\n    that = that.slice(0, actual);\n  }\n\n  return that\n}\n\nfunction fromArrayLike (that, array) {\n  var length = array.length < 0 ? 0 : checked(array.length) | 0;\n  that = createBuffer(that, length);\n  for (var i = 0; i < length; i += 1) {\n    that[i] = array[i] & 255;\n  }\n  return that\n}\n\nfunction fromArrayBuffer (that, array, byteOffset, length) {\n  array.byteLength; // this throws if `array` is not a valid ArrayBuffer\n\n  if (byteOffset < 0 || array.byteLength < byteOffset) {\n    throw new RangeError('\\'offset\\' is out of bounds')\n  }\n\n  if (array.byteLength < byteOffset + (length || 0)) {\n    throw new RangeError('\\'length\\' is out of bounds')\n  }\n\n  if (byteOffset === undefined && length === undefined) {\n    array = new Uint8Array(array);\n  } else if (length === undefined) {\n    array = new Uint8Array(array, byteOffset);\n  } else {\n    array = new Uint8Array(array, byteOffset, length);\n  }\n\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    // Return an augmented `Uint8Array` instance, for best performance\n    that = array;\n    that.__proto__ = Buffer.prototype;\n  } else {\n    // Fallback: Return an object instance of the Buffer class\n    that = fromArrayLike(that, array);\n  }\n  return that\n}\n\nfunction fromObject (that, obj) {\n  if (internalIsBuffer(obj)) {\n    var len = checked(obj.length) | 0;\n    that = createBuffer(that, len);\n\n    if (that.length === 0) {\n      return that\n    }\n\n    obj.copy(that, 0, 0, len);\n    return that\n  }\n\n  if (obj) {\n    if ((typeof ArrayBuffer !== 'undefined' &&\n        obj.buffer instanceof ArrayBuffer) || 'length' in obj) {\n      if (typeof obj.length !== 'number' || isnan(obj.length)) {\n        return createBuffer(that, 0)\n      }\n      return fromArrayLike(that, obj)\n    }\n\n    if (obj.type === 'Buffer' && isArray(obj.data)) {\n      return fromArrayLike(that, obj.data)\n    }\n  }\n\n  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')\n}\n\nfunction checked (length) {\n  // Note: cannot use `length < kMaxLength()` here because that fails when\n  // length is NaN (which is otherwise coerced to zero.)\n  if (length >= kMaxLength()) {\n    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +\n                         'size: 0x' + kMaxLength().toString(16) + ' bytes')\n  }\n  return length | 0\n}\nBuffer.isBuffer = isBuffer;\nfunction internalIsBuffer (b) {\n  return !!(b != null && b._isBuffer)\n}\n\nBuffer.compare = function compare (a, b) {\n  if (!internalIsBuffer(a) || !internalIsBuffer(b)) {\n    throw new TypeError('Arguments must be Buffers')\n  }\n\n  if (a === b) return 0\n\n  var x = a.length;\n  var y = b.length;\n\n  for (var i = 0, len = Math.min(x, y); i < len; ++i) {\n    if (a[i] !== b[i]) {\n      x = a[i];\n      y = b[i];\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n};\n\nBuffer.isEncoding = function isEncoding (encoding) {\n  switch (String(encoding).toLowerCase()) {\n    case 'hex':\n    case 'utf8':\n    case 'utf-8':\n    case 'ascii':\n    case 'latin1':\n    case 'binary':\n    case 'base64':\n    case 'ucs2':\n    case 'ucs-2':\n    case 'utf16le':\n    case 'utf-16le':\n      return true\n    default:\n      return false\n  }\n};\n\nBuffer.concat = function concat (list, length) {\n  if (!isArray(list)) {\n    throw new TypeError('\"list\" argument must be an Array of Buffers')\n  }\n\n  if (list.length === 0) {\n    return Buffer.alloc(0)\n  }\n\n  var i;\n  if (length === undefined) {\n    length = 0;\n    for (i = 0; i < list.length; ++i) {\n      length += list[i].length;\n    }\n  }\n\n  var buffer = Buffer.allocUnsafe(length);\n  var pos = 0;\n  for (i = 0; i < list.length; ++i) {\n    var buf = list[i];\n    if (!internalIsBuffer(buf)) {\n      throw new TypeError('\"list\" argument must be an Array of Buffers')\n    }\n    buf.copy(buffer, pos);\n    pos += buf.length;\n  }\n  return buffer\n};\n\nfunction byteLength (string, encoding) {\n  if (internalIsBuffer(string)) {\n    return string.length\n  }\n  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' &&\n      (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {\n    return string.byteLength\n  }\n  if (typeof string !== 'string') {\n    string = '' + string;\n  }\n\n  var len = string.length;\n  if (len === 0) return 0\n\n  // Use a for loop to avoid recursion\n  var loweredCase = false;\n  for (;;) {\n    switch (encoding) {\n      case 'ascii':\n      case 'latin1':\n      case 'binary':\n        return len\n      case 'utf8':\n      case 'utf-8':\n      case undefined:\n        return utf8ToBytes(string).length\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return len * 2\n      case 'hex':\n        return len >>> 1\n      case 'base64':\n        return base64ToBytes(string).length\n      default:\n        if (loweredCase) return utf8ToBytes(string).length // assume utf8\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\nBuffer.byteLength = byteLength;\n\nfunction slowToString (encoding, start, end) {\n  var loweredCase = false;\n\n  // No need to verify that \"this.length <= MAX_UINT32\" since it's a read-only\n  // property of a typed array.\n\n  // This behaves neither like String nor Uint8Array in that we set start/end\n  // to their upper/lower bounds if the value passed is out of range.\n  // undefined is handled specially as per ECMA-262 6th Edition,\n  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.\n  if (start === undefined || start < 0) {\n    start = 0;\n  }\n  // Return early if start > this.length. Done here to prevent potential uint32\n  // coercion fail below.\n  if (start > this.length) {\n    return ''\n  }\n\n  if (end === undefined || end > this.length) {\n    end = this.length;\n  }\n\n  if (end <= 0) {\n    return ''\n  }\n\n  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.\n  end >>>= 0;\n  start >>>= 0;\n\n  if (end <= start) {\n    return ''\n  }\n\n  if (!encoding) encoding = 'utf8';\n\n  while (true) {\n    switch (encoding) {\n      case 'hex':\n        return hexSlice(this, start, end)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Slice(this, start, end)\n\n      case 'ascii':\n        return asciiSlice(this, start, end)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Slice(this, start, end)\n\n      case 'base64':\n        return base64Slice(this, start, end)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return utf16leSlice(this, start, end)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = (encoding + '').toLowerCase();\n        loweredCase = true;\n    }\n  }\n}\n\n// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect\n// Buffer instances.\nBuffer.prototype._isBuffer = true;\n\nfunction swap (b, n, m) {\n  var i = b[n];\n  b[n] = b[m];\n  b[m] = i;\n}\n\nBuffer.prototype.swap16 = function swap16 () {\n  var len = this.length;\n  if (len % 2 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 16-bits')\n  }\n  for (var i = 0; i < len; i += 2) {\n    swap(this, i, i + 1);\n  }\n  return this\n};\n\nBuffer.prototype.swap32 = function swap32 () {\n  var len = this.length;\n  if (len % 4 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 32-bits')\n  }\n  for (var i = 0; i < len; i += 4) {\n    swap(this, i, i + 3);\n    swap(this, i + 1, i + 2);\n  }\n  return this\n};\n\nBuffer.prototype.swap64 = function swap64 () {\n  var len = this.length;\n  if (len % 8 !== 0) {\n    throw new RangeError('Buffer size must be a multiple of 64-bits')\n  }\n  for (var i = 0; i < len; i += 8) {\n    swap(this, i, i + 7);\n    swap(this, i + 1, i + 6);\n    swap(this, i + 2, i + 5);\n    swap(this, i + 3, i + 4);\n  }\n  return this\n};\n\nBuffer.prototype.toString = function toString () {\n  var length = this.length | 0;\n  if (length === 0) return ''\n  if (arguments.length === 0) return utf8Slice(this, 0, length)\n  return slowToString.apply(this, arguments)\n};\n\nBuffer.prototype.equals = function equals (b) {\n  if (!internalIsBuffer(b)) throw new TypeError('Argument must be a Buffer')\n  if (this === b) return true\n  return Buffer.compare(this, b) === 0\n};\n\nBuffer.prototype.inspect = function inspect () {\n  var str = '';\n  var max = INSPECT_MAX_BYTES;\n  if (this.length > 0) {\n    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ');\n    if (this.length > max) str += ' ... ';\n  }\n  return '<Buffer ' + str + '>'\n};\n\nBuffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {\n  if (!internalIsBuffer(target)) {\n    throw new TypeError('Argument must be a Buffer')\n  }\n\n  if (start === undefined) {\n    start = 0;\n  }\n  if (end === undefined) {\n    end = target ? target.length : 0;\n  }\n  if (thisStart === undefined) {\n    thisStart = 0;\n  }\n  if (thisEnd === undefined) {\n    thisEnd = this.length;\n  }\n\n  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {\n    throw new RangeError('out of range index')\n  }\n\n  if (thisStart >= thisEnd && start >= end) {\n    return 0\n  }\n  if (thisStart >= thisEnd) {\n    return -1\n  }\n  if (start >= end) {\n    return 1\n  }\n\n  start >>>= 0;\n  end >>>= 0;\n  thisStart >>>= 0;\n  thisEnd >>>= 0;\n\n  if (this === target) return 0\n\n  var x = thisEnd - thisStart;\n  var y = end - start;\n  var len = Math.min(x, y);\n\n  var thisCopy = this.slice(thisStart, thisEnd);\n  var targetCopy = target.slice(start, end);\n\n  for (var i = 0; i < len; ++i) {\n    if (thisCopy[i] !== targetCopy[i]) {\n      x = thisCopy[i];\n      y = targetCopy[i];\n      break\n    }\n  }\n\n  if (x < y) return -1\n  if (y < x) return 1\n  return 0\n};\n\n// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,\n// OR the last index of `val` in `buffer` at offset <= `byteOffset`.\n//\n// Arguments:\n// - buffer - a Buffer to search\n// - val - a string, Buffer, or number\n// - byteOffset - an index into `buffer`; will be clamped to an int32\n// - encoding - an optional encoding, relevant is val is a string\n// - dir - true for indexOf, false for lastIndexOf\nfunction bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {\n  // Empty buffer means no match\n  if (buffer.length === 0) return -1\n\n  // Normalize byteOffset\n  if (typeof byteOffset === 'string') {\n    encoding = byteOffset;\n    byteOffset = 0;\n  } else if (byteOffset > 0x7fffffff) {\n    byteOffset = 0x7fffffff;\n  } else if (byteOffset < -0x80000000) {\n    byteOffset = -0x80000000;\n  }\n  byteOffset = +byteOffset;  // Coerce to Number.\n  if (isNaN(byteOffset)) {\n    // byteOffset: it it's undefined, null, NaN, \"foo\", etc, search whole buffer\n    byteOffset = dir ? 0 : (buffer.length - 1);\n  }\n\n  // Normalize byteOffset: negative offsets start from the end of the buffer\n  if (byteOffset < 0) byteOffset = buffer.length + byteOffset;\n  if (byteOffset >= buffer.length) {\n    if (dir) return -1\n    else byteOffset = buffer.length - 1;\n  } else if (byteOffset < 0) {\n    if (dir) byteOffset = 0;\n    else return -1\n  }\n\n  // Normalize val\n  if (typeof val === 'string') {\n    val = Buffer.from(val, encoding);\n  }\n\n  // Finally, search either indexOf (if dir is true) or lastIndexOf\n  if (internalIsBuffer(val)) {\n    // Special case: looking for empty string/buffer always fails\n    if (val.length === 0) {\n      return -1\n    }\n    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)\n  } else if (typeof val === 'number') {\n    val = val & 0xFF; // Search for a byte value [0-255]\n    if (Buffer.TYPED_ARRAY_SUPPORT &&\n        typeof Uint8Array.prototype.indexOf === 'function') {\n      if (dir) {\n        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)\n      } else {\n        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)\n      }\n    }\n    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)\n  }\n\n  throw new TypeError('val must be string, number or Buffer')\n}\n\nfunction arrayIndexOf (arr, val, byteOffset, encoding, dir) {\n  var indexSize = 1;\n  var arrLength = arr.length;\n  var valLength = val.length;\n\n  if (encoding !== undefined) {\n    encoding = String(encoding).toLowerCase();\n    if (encoding === 'ucs2' || encoding === 'ucs-2' ||\n        encoding === 'utf16le' || encoding === 'utf-16le') {\n      if (arr.length < 2 || val.length < 2) {\n        return -1\n      }\n      indexSize = 2;\n      arrLength /= 2;\n      valLength /= 2;\n      byteOffset /= 2;\n    }\n  }\n\n  function read (buf, i) {\n    if (indexSize === 1) {\n      return buf[i]\n    } else {\n      return buf.readUInt16BE(i * indexSize)\n    }\n  }\n\n  var i;\n  if (dir) {\n    var foundIndex = -1;\n    for (i = byteOffset; i < arrLength; i++) {\n      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {\n        if (foundIndex === -1) foundIndex = i;\n        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize\n      } else {\n        if (foundIndex !== -1) i -= i - foundIndex;\n        foundIndex = -1;\n      }\n    }\n  } else {\n    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength;\n    for (i = byteOffset; i >= 0; i--) {\n      var found = true;\n      for (var j = 0; j < valLength; j++) {\n        if (read(arr, i + j) !== read(val, j)) {\n          found = false;\n          break\n        }\n      }\n      if (found) return i\n    }\n  }\n\n  return -1\n}\n\nBuffer.prototype.includes = function includes (val, byteOffset, encoding) {\n  return this.indexOf(val, byteOffset, encoding) !== -1\n};\n\nBuffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)\n};\n\nBuffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {\n  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)\n};\n\nfunction hexWrite (buf, string, offset, length) {\n  offset = Number(offset) || 0;\n  var remaining = buf.length - offset;\n  if (!length) {\n    length = remaining;\n  } else {\n    length = Number(length);\n    if (length > remaining) {\n      length = remaining;\n    }\n  }\n\n  // must be an even number of digits\n  var strLen = string.length;\n  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string')\n\n  if (length > strLen / 2) {\n    length = strLen / 2;\n  }\n  for (var i = 0; i < length; ++i) {\n    var parsed = parseInt(string.substr(i * 2, 2), 16);\n    if (isNaN(parsed)) return i\n    buf[offset + i] = parsed;\n  }\n  return i\n}\n\nfunction utf8Write (buf, string, offset, length) {\n  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nfunction asciiWrite (buf, string, offset, length) {\n  return blitBuffer(asciiToBytes(string), buf, offset, length)\n}\n\nfunction latin1Write (buf, string, offset, length) {\n  return asciiWrite(buf, string, offset, length)\n}\n\nfunction base64Write (buf, string, offset, length) {\n  return blitBuffer(base64ToBytes(string), buf, offset, length)\n}\n\nfunction ucs2Write (buf, string, offset, length) {\n  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)\n}\n\nBuffer.prototype.write = function write (string, offset, length, encoding) {\n  // Buffer#write(string)\n  if (offset === undefined) {\n    encoding = 'utf8';\n    length = this.length;\n    offset = 0;\n  // Buffer#write(string, encoding)\n  } else if (length === undefined && typeof offset === 'string') {\n    encoding = offset;\n    length = this.length;\n    offset = 0;\n  // Buffer#write(string, offset[, length][, encoding])\n  } else if (isFinite(offset)) {\n    offset = offset | 0;\n    if (isFinite(length)) {\n      length = length | 0;\n      if (encoding === undefined) encoding = 'utf8';\n    } else {\n      encoding = length;\n      length = undefined;\n    }\n  // legacy write(string, encoding, offset, length) - remove in v0.13\n  } else {\n    throw new Error(\n      'Buffer.write(string, encoding, offset[, length]) is no longer supported'\n    )\n  }\n\n  var remaining = this.length - offset;\n  if (length === undefined || length > remaining) length = remaining;\n\n  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {\n    throw new RangeError('Attempt to write outside buffer bounds')\n  }\n\n  if (!encoding) encoding = 'utf8';\n\n  var loweredCase = false;\n  for (;;) {\n    switch (encoding) {\n      case 'hex':\n        return hexWrite(this, string, offset, length)\n\n      case 'utf8':\n      case 'utf-8':\n        return utf8Write(this, string, offset, length)\n\n      case 'ascii':\n        return asciiWrite(this, string, offset, length)\n\n      case 'latin1':\n      case 'binary':\n        return latin1Write(this, string, offset, length)\n\n      case 'base64':\n        // Warning: maxLength not taken into account in base64Write\n        return base64Write(this, string, offset, length)\n\n      case 'ucs2':\n      case 'ucs-2':\n      case 'utf16le':\n      case 'utf-16le':\n        return ucs2Write(this, string, offset, length)\n\n      default:\n        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)\n        encoding = ('' + encoding).toLowerCase();\n        loweredCase = true;\n    }\n  }\n};\n\nBuffer.prototype.toJSON = function toJSON () {\n  return {\n    type: 'Buffer',\n    data: Array.prototype.slice.call(this._arr || this, 0)\n  }\n};\n\nfunction base64Slice (buf, start, end) {\n  if (start === 0 && end === buf.length) {\n    return fromByteArray(buf)\n  } else {\n    return fromByteArray(buf.slice(start, end))\n  }\n}\n\nfunction utf8Slice (buf, start, end) {\n  end = Math.min(buf.length, end);\n  var res = [];\n\n  var i = start;\n  while (i < end) {\n    var firstByte = buf[i];\n    var codePoint = null;\n    var bytesPerSequence = (firstByte > 0xEF) ? 4\n      : (firstByte > 0xDF) ? 3\n      : (firstByte > 0xBF) ? 2\n      : 1;\n\n    if (i + bytesPerSequence <= end) {\n      var secondByte, thirdByte, fourthByte, tempCodePoint;\n\n      switch (bytesPerSequence) {\n        case 1:\n          if (firstByte < 0x80) {\n            codePoint = firstByte;\n          }\n          break\n        case 2:\n          secondByte = buf[i + 1];\n          if ((secondByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F);\n            if (tempCodePoint > 0x7F) {\n              codePoint = tempCodePoint;\n            }\n          }\n          break\n        case 3:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F);\n            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {\n              codePoint = tempCodePoint;\n            }\n          }\n          break\n        case 4:\n          secondByte = buf[i + 1];\n          thirdByte = buf[i + 2];\n          fourthByte = buf[i + 3];\n          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {\n            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F);\n            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {\n              codePoint = tempCodePoint;\n            }\n          }\n      }\n    }\n\n    if (codePoint === null) {\n      // we did not generate a valid codePoint so insert a\n      // replacement char (U+FFFD) and advance only 1 byte\n      codePoint = 0xFFFD;\n      bytesPerSequence = 1;\n    } else if (codePoint > 0xFFFF) {\n      // encode to utf16 (surrogate pair dance)\n      codePoint -= 0x10000;\n      res.push(codePoint >>> 10 & 0x3FF | 0xD800);\n      codePoint = 0xDC00 | codePoint & 0x3FF;\n    }\n\n    res.push(codePoint);\n    i += bytesPerSequence;\n  }\n\n  return decodeCodePointsArray(res)\n}\n\n// Based on http://stackoverflow.com/a/22747272/680742, the browser with\n// the lowest limit is Chrome, with 0x10000 args.\n// We go 1 magnitude less, for safety\nvar MAX_ARGUMENTS_LENGTH = 0x1000;\n\nfunction decodeCodePointsArray (codePoints) {\n  var len = codePoints.length;\n  if (len <= MAX_ARGUMENTS_LENGTH) {\n    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()\n  }\n\n  // Decode in chunks to avoid \"call stack size exceeded\".\n  var res = '';\n  var i = 0;\n  while (i < len) {\n    res += String.fromCharCode.apply(\n      String,\n      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)\n    );\n  }\n  return res\n}\n\nfunction asciiSlice (buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i] & 0x7F);\n  }\n  return ret\n}\n\nfunction latin1Slice (buf, start, end) {\n  var ret = '';\n  end = Math.min(buf.length, end);\n\n  for (var i = start; i < end; ++i) {\n    ret += String.fromCharCode(buf[i]);\n  }\n  return ret\n}\n\nfunction hexSlice (buf, start, end) {\n  var len = buf.length;\n\n  if (!start || start < 0) start = 0;\n  if (!end || end < 0 || end > len) end = len;\n\n  var out = '';\n  for (var i = start; i < end; ++i) {\n    out += toHex(buf[i]);\n  }\n  return out\n}\n\nfunction utf16leSlice (buf, start, end) {\n  var bytes = buf.slice(start, end);\n  var res = '';\n  for (var i = 0; i < bytes.length; i += 2) {\n    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256);\n  }\n  return res\n}\n\nBuffer.prototype.slice = function slice (start, end) {\n  var len = this.length;\n  start = ~~start;\n  end = end === undefined ? len : ~~end;\n\n  if (start < 0) {\n    start += len;\n    if (start < 0) start = 0;\n  } else if (start > len) {\n    start = len;\n  }\n\n  if (end < 0) {\n    end += len;\n    if (end < 0) end = 0;\n  } else if (end > len) {\n    end = len;\n  }\n\n  if (end < start) end = start;\n\n  var newBuf;\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    newBuf = this.subarray(start, end);\n    newBuf.__proto__ = Buffer.prototype;\n  } else {\n    var sliceLen = end - start;\n    newBuf = new Buffer(sliceLen, undefined);\n    for (var i = 0; i < sliceLen; ++i) {\n      newBuf[i] = this[i + start];\n    }\n  }\n\n  return newBuf\n};\n\n/*\n * Need to make sure that buffer isn't trying to write out of bounds.\n */\nfunction checkOffset (offset, ext, length) {\n  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')\n  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')\n}\n\nBuffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n\n  return val\n};\n\nBuffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) {\n    checkOffset(offset, byteLength, this.length);\n  }\n\n  var val = this[offset + --byteLength];\n  var mul = 1;\n  while (byteLength > 0 && (mul *= 0x100)) {\n    val += this[offset + --byteLength] * mul;\n  }\n\n  return val\n};\n\nBuffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  return this[offset]\n};\n\nBuffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return this[offset] | (this[offset + 1] << 8)\n};\n\nBuffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  return (this[offset] << 8) | this[offset + 1]\n};\n\nBuffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n\n  return ((this[offset]) |\n      (this[offset + 1] << 8) |\n      (this[offset + 2] << 16)) +\n      (this[offset + 3] * 0x1000000)\n};\n\nBuffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n\n  return (this[offset] * 0x1000000) +\n    ((this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    this[offset + 3])\n};\n\nBuffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n\n  var val = this[offset];\n  var mul = 1;\n  var i = 0;\n  while (++i < byteLength && (mul *= 0x100)) {\n    val += this[offset + i] * mul;\n  }\n  mul *= 0x80;\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n\n  return val\n};\n\nBuffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) checkOffset(offset, byteLength, this.length);\n\n  var i = byteLength;\n  var mul = 1;\n  var val = this[offset + --i];\n  while (i > 0 && (mul *= 0x100)) {\n    val += this[offset + --i] * mul;\n  }\n  mul *= 0x80;\n\n  if (val >= mul) val -= Math.pow(2, 8 * byteLength);\n\n  return val\n};\n\nBuffer.prototype.readInt8 = function readInt8 (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 1, this.length);\n  if (!(this[offset] & 0x80)) return (this[offset])\n  return ((0xff - this[offset] + 1) * -1)\n};\n\nBuffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset] | (this[offset + 1] << 8);\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n};\n\nBuffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 2, this.length);\n  var val = this[offset + 1] | (this[offset] << 8);\n  return (val & 0x8000) ? val | 0xFFFF0000 : val\n};\n\nBuffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n\n  return (this[offset]) |\n    (this[offset + 1] << 8) |\n    (this[offset + 2] << 16) |\n    (this[offset + 3] << 24)\n};\n\nBuffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n\n  return (this[offset] << 24) |\n    (this[offset + 1] << 16) |\n    (this[offset + 2] << 8) |\n    (this[offset + 3])\n};\n\nBuffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return read(this, offset, true, 23, 4)\n};\n\nBuffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 4, this.length);\n  return read(this, offset, false, 23, 4)\n};\n\nBuffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return read(this, offset, true, 52, 8)\n};\n\nBuffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {\n  if (!noAssert) checkOffset(offset, 8, this.length);\n  return read(this, offset, false, 52, 8)\n};\n\nfunction checkInt (buf, value, offset, ext, max, min) {\n  if (!internalIsBuffer(buf)) throw new TypeError('\"buffer\" argument must be a Buffer instance')\n  if (value > max || value < min) throw new RangeError('\"value\" argument is out of bounds')\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n}\n\nBuffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var mul = 1;\n  var i = 0;\n  this[offset] = value & 0xFF;\n  while (++i < byteLength && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF;\n  }\n\n  return offset + byteLength\n};\n\nBuffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  byteLength = byteLength | 0;\n  if (!noAssert) {\n    var maxBytes = Math.pow(2, 8 * byteLength) - 1;\n    checkInt(this, value, offset, byteLength, maxBytes, 0);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  this[offset + i] = value & 0xFF;\n  while (--i >= 0 && (mul *= 0x100)) {\n    this[offset + i] = (value / mul) & 0xFF;\n  }\n\n  return offset + byteLength\n};\n\nBuffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  this[offset] = (value & 0xff);\n  return offset + 1\n};\n\nfunction objectWriteUInt16 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffff + value + 1;\n  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {\n    buf[offset + i] = (value & (0xff << (8 * (littleEndian ? i : 1 - i)))) >>>\n      (littleEndian ? i : 1 - i) * 8;\n  }\n}\n\nBuffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff);\n    this[offset + 1] = (value >>> 8);\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n  return offset + 2\n};\n\nBuffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8);\n    this[offset + 1] = (value & 0xff);\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n  return offset + 2\n};\n\nfunction objectWriteUInt32 (buf, value, offset, littleEndian) {\n  if (value < 0) value = 0xffffffff + value + 1;\n  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {\n    buf[offset + i] = (value >>> (littleEndian ? i : 3 - i) * 8) & 0xff;\n  }\n}\n\nBuffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset + 3] = (value >>> 24);\n    this[offset + 2] = (value >>> 16);\n    this[offset + 1] = (value >>> 8);\n    this[offset] = (value & 0xff);\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n  return offset + 4\n};\n\nBuffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24);\n    this[offset + 1] = (value >>> 16);\n    this[offset + 2] = (value >>> 8);\n    this[offset + 3] = (value & 0xff);\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n  return offset + 4\n};\n\nBuffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = 0;\n  var mul = 1;\n  var sub = 0;\n  this[offset] = value & 0xFF;\n  while (++i < byteLength && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {\n      sub = 1;\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength\n};\n\nBuffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) {\n    var limit = Math.pow(2, 8 * byteLength - 1);\n\n    checkInt(this, value, offset, byteLength, limit - 1, -limit);\n  }\n\n  var i = byteLength - 1;\n  var mul = 1;\n  var sub = 0;\n  this[offset + i] = value & 0xFF;\n  while (--i >= 0 && (mul *= 0x100)) {\n    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {\n      sub = 1;\n    }\n    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF;\n  }\n\n  return offset + byteLength\n};\n\nBuffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80);\n  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value);\n  if (value < 0) value = 0xff + value + 1;\n  this[offset] = (value & 0xff);\n  return offset + 1\n};\n\nBuffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff);\n    this[offset + 1] = (value >>> 8);\n  } else {\n    objectWriteUInt16(this, value, offset, true);\n  }\n  return offset + 2\n};\n\nBuffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 8);\n    this[offset + 1] = (value & 0xff);\n  } else {\n    objectWriteUInt16(this, value, offset, false);\n  }\n  return offset + 2\n};\n\nBuffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value & 0xff);\n    this[offset + 1] = (value >>> 8);\n    this[offset + 2] = (value >>> 16);\n    this[offset + 3] = (value >>> 24);\n  } else {\n    objectWriteUInt32(this, value, offset, true);\n  }\n  return offset + 4\n};\n\nBuffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {\n  value = +value;\n  offset = offset | 0;\n  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000);\n  if (value < 0) value = 0xffffffff + value + 1;\n  if (Buffer.TYPED_ARRAY_SUPPORT) {\n    this[offset] = (value >>> 24);\n    this[offset + 1] = (value >>> 16);\n    this[offset + 2] = (value >>> 8);\n    this[offset + 3] = (value & 0xff);\n  } else {\n    objectWriteUInt32(this, value, offset, false);\n  }\n  return offset + 4\n};\n\nfunction checkIEEE754 (buf, value, offset, ext, max, min) {\n  if (offset + ext > buf.length) throw new RangeError('Index out of range')\n  if (offset < 0) throw new RangeError('Index out of range')\n}\n\nfunction writeFloat (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 4);\n  }\n  write(buf, value, offset, littleEndian, 23, 4);\n  return offset + 4\n}\n\nBuffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, true, noAssert)\n};\n\nBuffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {\n  return writeFloat(this, value, offset, false, noAssert)\n};\n\nfunction writeDouble (buf, value, offset, littleEndian, noAssert) {\n  if (!noAssert) {\n    checkIEEE754(buf, value, offset, 8);\n  }\n  write(buf, value, offset, littleEndian, 52, 8);\n  return offset + 8\n}\n\nBuffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, true, noAssert)\n};\n\nBuffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {\n  return writeDouble(this, value, offset, false, noAssert)\n};\n\n// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)\nBuffer.prototype.copy = function copy (target, targetStart, start, end) {\n  if (!start) start = 0;\n  if (!end && end !== 0) end = this.length;\n  if (targetStart >= target.length) targetStart = target.length;\n  if (!targetStart) targetStart = 0;\n  if (end > 0 && end < start) end = start;\n\n  // Copy 0 bytes; we're done\n  if (end === start) return 0\n  if (target.length === 0 || this.length === 0) return 0\n\n  // Fatal error conditions\n  if (targetStart < 0) {\n    throw new RangeError('targetStart out of bounds')\n  }\n  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds')\n  if (end < 0) throw new RangeError('sourceEnd out of bounds')\n\n  // Are we oob?\n  if (end > this.length) end = this.length;\n  if (target.length - targetStart < end - start) {\n    end = target.length - targetStart + start;\n  }\n\n  var len = end - start;\n  var i;\n\n  if (this === target && start < targetStart && targetStart < end) {\n    // descending copy from end\n    for (i = len - 1; i >= 0; --i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {\n    // ascending copy from start\n    for (i = 0; i < len; ++i) {\n      target[i + targetStart] = this[i + start];\n    }\n  } else {\n    Uint8Array.prototype.set.call(\n      target,\n      this.subarray(start, start + len),\n      targetStart\n    );\n  }\n\n  return len\n};\n\n// Usage:\n//    buffer.fill(number[, offset[, end]])\n//    buffer.fill(buffer[, offset[, end]])\n//    buffer.fill(string[, offset[, end]][, encoding])\nBuffer.prototype.fill = function fill (val, start, end, encoding) {\n  // Handle string cases:\n  if (typeof val === 'string') {\n    if (typeof start === 'string') {\n      encoding = start;\n      start = 0;\n      end = this.length;\n    } else if (typeof end === 'string') {\n      encoding = end;\n      end = this.length;\n    }\n    if (val.length === 1) {\n      var code = val.charCodeAt(0);\n      if (code < 256) {\n        val = code;\n      }\n    }\n    if (encoding !== undefined && typeof encoding !== 'string') {\n      throw new TypeError('encoding must be a string')\n    }\n    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {\n      throw new TypeError('Unknown encoding: ' + encoding)\n    }\n  } else if (typeof val === 'number') {\n    val = val & 255;\n  }\n\n  // Invalid ranges are not set to a default, so can range check early.\n  if (start < 0 || this.length < start || this.length < end) {\n    throw new RangeError('Out of range index')\n  }\n\n  if (end <= start) {\n    return this\n  }\n\n  start = start >>> 0;\n  end = end === undefined ? this.length : end >>> 0;\n\n  if (!val) val = 0;\n\n  var i;\n  if (typeof val === 'number') {\n    for (i = start; i < end; ++i) {\n      this[i] = val;\n    }\n  } else {\n    var bytes = internalIsBuffer(val)\n      ? val\n      : utf8ToBytes(new Buffer(val, encoding).toString());\n    var len = bytes.length;\n    for (i = 0; i < end - start; ++i) {\n      this[i + start] = bytes[i % len];\n    }\n  }\n\n  return this\n};\n\n// HELPER FUNCTIONS\n// ================\n\nvar INVALID_BASE64_RE = /[^+\\/0-9A-Za-z-_]/g;\n\nfunction base64clean (str) {\n  // Node strips out invalid characters like \\n and \\t from the string, base64-js does not\n  str = stringtrim(str).replace(INVALID_BASE64_RE, '');\n  // Node converts strings with length < 2 to ''\n  if (str.length < 2) return ''\n  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not\n  while (str.length % 4 !== 0) {\n    str = str + '=';\n  }\n  return str\n}\n\nfunction stringtrim (str) {\n  if (str.trim) return str.trim()\n  return str.replace(/^\\s+|\\s+$/g, '')\n}\n\nfunction toHex (n) {\n  if (n < 16) return '0' + n.toString(16)\n  return n.toString(16)\n}\n\nfunction utf8ToBytes (string, units) {\n  units = units || Infinity;\n  var codePoint;\n  var length = string.length;\n  var leadSurrogate = null;\n  var bytes = [];\n\n  for (var i = 0; i < length; ++i) {\n    codePoint = string.charCodeAt(i);\n\n    // is surrogate component\n    if (codePoint > 0xD7FF && codePoint < 0xE000) {\n      // last char was a lead\n      if (!leadSurrogate) {\n        // no lead yet\n        if (codePoint > 0xDBFF) {\n          // unexpected trail\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue\n        } else if (i + 1 === length) {\n          // unpaired lead\n          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n          continue\n        }\n\n        // valid lead\n        leadSurrogate = codePoint;\n\n        continue\n      }\n\n      // 2 leads in a row\n      if (codePoint < 0xDC00) {\n        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n        leadSurrogate = codePoint;\n        continue\n      }\n\n      // valid surrogate pair\n      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000;\n    } else if (leadSurrogate) {\n      // valid bmp char, but last char was a lead\n      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD);\n    }\n\n    leadSurrogate = null;\n\n    // encode utf8\n    if (codePoint < 0x80) {\n      if ((units -= 1) < 0) break\n      bytes.push(codePoint);\n    } else if (codePoint < 0x800) {\n      if ((units -= 2) < 0) break\n      bytes.push(\n        codePoint >> 0x6 | 0xC0,\n        codePoint & 0x3F | 0x80\n      );\n    } else if (codePoint < 0x10000) {\n      if ((units -= 3) < 0) break\n      bytes.push(\n        codePoint >> 0xC | 0xE0,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      );\n    } else if (codePoint < 0x110000) {\n      if ((units -= 4) < 0) break\n      bytes.push(\n        codePoint >> 0x12 | 0xF0,\n        codePoint >> 0xC & 0x3F | 0x80,\n        codePoint >> 0x6 & 0x3F | 0x80,\n        codePoint & 0x3F | 0x80\n      );\n    } else {\n      throw new Error('Invalid code point')\n    }\n  }\n\n  return bytes\n}\n\nfunction asciiToBytes (str) {\n  var byteArray = [];\n  for (var i = 0; i < str.length; ++i) {\n    // Node's code seems to be doing this and not & 0x7F..\n    byteArray.push(str.charCodeAt(i) & 0xFF);\n  }\n  return byteArray\n}\n\nfunction utf16leToBytes (str, units) {\n  var c, hi, lo;\n  var byteArray = [];\n  for (var i = 0; i < str.length; ++i) {\n    if ((units -= 2) < 0) break\n\n    c = str.charCodeAt(i);\n    hi = c >> 8;\n    lo = c % 256;\n    byteArray.push(lo);\n    byteArray.push(hi);\n  }\n\n  return byteArray\n}\n\n\nfunction base64ToBytes (str) {\n  return toByteArray(base64clean(str))\n}\n\nfunction blitBuffer (src, dst, offset, length) {\n  for (var i = 0; i < length; ++i) {\n    if ((i + offset >= dst.length) || (i >= src.length)) break\n    dst[i + offset] = src[i];\n  }\n  return i\n}\n\nfunction isnan (val) {\n  return val !== val // eslint-disable-line no-self-compare\n}\n\n\n// the following is from is-buffer, also by Feross Aboukhadijeh and with same lisence\n// The _isBuffer check is for Safari 5-7 support, because it's missing\n// Object.prototype.constructor. Remove this eventually\nfunction isBuffer(obj) {\n  return obj != null && (!!obj._isBuffer || isFastBuffer(obj) || isSlowBuffer(obj))\n}\n\nfunction isFastBuffer (obj) {\n  return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)\n}\n\n// For Node v0.10 support. Remove this eventually.\nfunction isSlowBuffer (obj) {\n  return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isFastBuffer(obj.slice(0, 0))\n}\n\nclass CsvError extends Error {\n  constructor(code, message, options, ...contexts) {\n    if (Array.isArray(message)) message = message.join(\" \").trim();\n    super(message);\n    if (Error.captureStackTrace !== undefined) {\n      Error.captureStackTrace(this, CsvError);\n    }\n    this.code = code;\n    for (const context of contexts) {\n      for (const key in context) {\n        const value = context[key];\n        this[key] = isBuffer(value)\n          ? value.toString(options.encoding)\n          : value == null\n            ? value\n            : JSON.parse(JSON.stringify(value));\n      }\n    }\n  }\n}\n\nconst is_object = function (obj) {\n  return typeof obj === \"object\" && obj !== null && !Array.isArray(obj);\n};\n\nconst normalize_columns_array = function (columns) {\n  const normalizedColumns = [];\n  for (let i = 0, l = columns.length; i < l; i++) {\n    const column = columns[i];\n    if (column === undefined || column === null || column === false) {\n      normalizedColumns[i] = { disabled: true };\n    } else if (typeof column === \"string\") {\n      normalizedColumns[i] = { name: column };\n    } else if (is_object(column)) {\n      if (typeof column.name !== \"string\") {\n        throw new CsvError(\"CSV_OPTION_COLUMNS_MISSING_NAME\", [\n          \"Option columns missing name:\",\n          `property \"name\" is required at position ${i}`,\n          \"when column is an object literal\",\n        ]);\n      }\n      normalizedColumns[i] = column;\n    } else {\n      throw new CsvError(\"CSV_INVALID_COLUMN_DEFINITION\", [\n        \"Invalid column definition:\",\n        \"expect a string or a literal object,\",\n        `got ${JSON.stringify(column)} at position ${i}`,\n      ]);\n    }\n  }\n  return normalizedColumns;\n};\n\nclass ResizeableBuffer {\n  constructor(size = 100) {\n    this.size = size;\n    this.length = 0;\n    this.buf = Buffer.allocUnsafe(size);\n  }\n  prepend(val) {\n    if (isBuffer(val)) {\n      const length = this.length + val.length;\n      if (length >= this.size) {\n        this.resize();\n        if (length >= this.size) {\n          throw Error(\"INVALID_BUFFER_STATE\");\n        }\n      }\n      const buf = this.buf;\n      this.buf = Buffer.allocUnsafe(this.size);\n      val.copy(this.buf, 0);\n      buf.copy(this.buf, val.length);\n      this.length += val.length;\n    } else {\n      const length = this.length++;\n      if (length === this.size) {\n        this.resize();\n      }\n      const buf = this.clone();\n      this.buf[0] = val;\n      buf.copy(this.buf, 1, 0, length);\n    }\n  }\n  append(val) {\n    const length = this.length++;\n    if (length === this.size) {\n      this.resize();\n    }\n    this.buf[length] = val;\n  }\n  clone() {\n    return Buffer.from(this.buf.slice(0, this.length));\n  }\n  resize() {\n    const length = this.length;\n    this.size = this.size * 2;\n    const buf = Buffer.allocUnsafe(this.size);\n    this.buf.copy(buf, 0, 0, length);\n    this.buf = buf;\n  }\n  toString(encoding) {\n    if (encoding) {\n      return this.buf.slice(0, this.length).toString(encoding);\n    } else {\n      return Uint8Array.prototype.slice.call(this.buf.slice(0, this.length));\n    }\n  }\n  toJSON() {\n    return this.toString(\"utf8\");\n  }\n  reset() {\n    this.length = 0;\n  }\n}\n\n// white space characters\n// https://en.wikipedia.org/wiki/Whitespace_character\n// https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions/Character_Classes#Types\n// \\f\\n\\r\\t\\v\\u00a0\\u1680\\u2000-\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff\nconst np = 12;\nconst cr$1 = 13; // `\\r`, carriage return, 0x0D in hexadécimal, 13 in decimal\nconst nl$1 = 10; // `\\n`, newline, 0x0A in hexadecimal, 10 in decimal\nconst space = 32;\nconst tab = 9;\n\nconst init_state = function (options) {\n  return {\n    bomSkipped: false,\n    bufBytesStart: 0,\n    castField: options.cast_function,\n    commenting: false,\n    // Current error encountered by a record\n    error: undefined,\n    enabled: options.from_line === 1,\n    escaping: false,\n    escapeIsQuote:\n      isBuffer(options.escape) &&\n      isBuffer(options.quote) &&\n      Buffer.compare(options.escape, options.quote) === 0,\n    // columns can be `false`, `true`, `Array`\n    expectedRecordLength: Array.isArray(options.columns)\n      ? options.columns.length\n      : undefined,\n    field: new ResizeableBuffer(20),\n    firstLineToHeaders: options.cast_first_line_to_header,\n    needMoreDataSize: Math.max(\n      // Skip if the remaining buffer smaller than comment\n      options.comment !== null ? options.comment.length : 0,\n      // Skip if the remaining buffer can be delimiter\n      ...options.delimiter.map((delimiter) => delimiter.length),\n      // Skip if the remaining buffer can be escape sequence\n      options.quote !== null ? options.quote.length : 0,\n    ),\n    previousBuf: undefined,\n    quoting: false,\n    stop: false,\n    rawBuffer: new ResizeableBuffer(100),\n    record: [],\n    recordHasError: false,\n    record_length: 0,\n    recordDelimiterMaxLength:\n      options.record_delimiter.length === 0\n        ? 0\n        : Math.max(...options.record_delimiter.map((v) => v.length)),\n    trimChars: [\n      Buffer.from(\" \", options.encoding)[0],\n      Buffer.from(\"\\t\", options.encoding)[0],\n    ],\n    wasQuoting: false,\n    wasRowDelimiter: false,\n    timchars: [\n      Buffer.from(Buffer.from([cr$1], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([nl$1], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([np], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([space], \"utf8\").toString(), options.encoding),\n      Buffer.from(Buffer.from([tab], \"utf8\").toString(), options.encoding),\n    ],\n  };\n};\n\nconst underscore = function (str) {\n  return str.replace(/([A-Z])/g, function (_, match) {\n    return \"_\" + match.toLowerCase();\n  });\n};\n\nconst normalize_options = function (opts) {\n  const options = {};\n  // Merge with user options\n  for (const opt in opts) {\n    options[underscore(opt)] = opts[opt];\n  }\n  // Normalize option `encoding`\n  // Note: defined first because other options depends on it\n  // to convert chars/strings into buffers.\n  if (options.encoding === undefined || options.encoding === true) {\n    options.encoding = \"utf8\";\n  } else if (options.encoding === null || options.encoding === false) {\n    options.encoding = null;\n  } else if (\n    typeof options.encoding !== \"string\" &&\n    options.encoding !== null\n  ) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_ENCODING\",\n      [\n        \"Invalid option encoding:\",\n        \"encoding must be a string or null to return a buffer,\",\n        `got ${JSON.stringify(options.encoding)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `bom`\n  if (\n    options.bom === undefined ||\n    options.bom === null ||\n    options.bom === false\n  ) {\n    options.bom = false;\n  } else if (options.bom !== true) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_BOM\",\n      [\n        \"Invalid option bom:\",\n        \"bom must be true,\",\n        `got ${JSON.stringify(options.bom)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `cast`\n  options.cast_function = null;\n  if (\n    options.cast === undefined ||\n    options.cast === null ||\n    options.cast === false ||\n    options.cast === \"\"\n  ) {\n    options.cast = undefined;\n  } else if (typeof options.cast === \"function\") {\n    options.cast_function = options.cast;\n    options.cast = true;\n  } else if (options.cast !== true) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_CAST\",\n      [\n        \"Invalid option cast:\",\n        \"cast must be true or a function,\",\n        `got ${JSON.stringify(options.cast)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `cast_date`\n  if (\n    options.cast_date === undefined ||\n    options.cast_date === null ||\n    options.cast_date === false ||\n    options.cast_date === \"\"\n  ) {\n    options.cast_date = false;\n  } else if (options.cast_date === true) {\n    options.cast_date = function (value) {\n      const date = Date.parse(value);\n      return !isNaN(date) ? new Date(date) : value;\n    };\n  } else if (typeof options.cast_date !== \"function\") {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_CAST_DATE\",\n      [\n        \"Invalid option cast_date:\",\n        \"cast_date must be true or a function,\",\n        `got ${JSON.stringify(options.cast_date)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `columns`\n  options.cast_first_line_to_header = null;\n  if (options.columns === true) {\n    // Fields in the first line are converted as-is to columns\n    options.cast_first_line_to_header = undefined;\n  } else if (typeof options.columns === \"function\") {\n    options.cast_first_line_to_header = options.columns;\n    options.columns = true;\n  } else if (Array.isArray(options.columns)) {\n    options.columns = normalize_columns_array(options.columns);\n  } else if (\n    options.columns === undefined ||\n    options.columns === null ||\n    options.columns === false\n  ) {\n    options.columns = false;\n  } else {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_COLUMNS\",\n      [\n        \"Invalid option columns:\",\n        \"expect an array, a function or true,\",\n        `got ${JSON.stringify(options.columns)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `group_columns_by_name`\n  if (\n    options.group_columns_by_name === undefined ||\n    options.group_columns_by_name === null ||\n    options.group_columns_by_name === false\n  ) {\n    options.group_columns_by_name = false;\n  } else if (options.group_columns_by_name !== true) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_GROUP_COLUMNS_BY_NAME\",\n      [\n        \"Invalid option group_columns_by_name:\",\n        \"expect an boolean,\",\n        `got ${JSON.stringify(options.group_columns_by_name)}`,\n      ],\n      options,\n    );\n  } else if (options.columns === false) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_GROUP_COLUMNS_BY_NAME\",\n      [\n        \"Invalid option group_columns_by_name:\",\n        \"the `columns` mode must be activated.\",\n      ],\n      options,\n    );\n  }\n  // Normalize option `comment`\n  if (\n    options.comment === undefined ||\n    options.comment === null ||\n    options.comment === false ||\n    options.comment === \"\"\n  ) {\n    options.comment = null;\n  } else {\n    if (typeof options.comment === \"string\") {\n      options.comment = Buffer.from(options.comment, options.encoding);\n    }\n    if (!isBuffer(options.comment)) {\n      throw new CsvError(\n        \"CSV_INVALID_OPTION_COMMENT\",\n        [\n          \"Invalid option comment:\",\n          \"comment must be a buffer or a string,\",\n          `got ${JSON.stringify(options.comment)}`,\n        ],\n        options,\n      );\n    }\n  }\n  // Normalize option `comment_no_infix`\n  if (\n    options.comment_no_infix === undefined ||\n    options.comment_no_infix === null ||\n    options.comment_no_infix === false\n  ) {\n    options.comment_no_infix = false;\n  } else if (options.comment_no_infix !== true) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_COMMENT\",\n      [\n        \"Invalid option comment_no_infix:\",\n        \"value must be a boolean,\",\n        `got ${JSON.stringify(options.comment_no_infix)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `delimiter`\n  const delimiter_json = JSON.stringify(options.delimiter);\n  if (!Array.isArray(options.delimiter))\n    options.delimiter = [options.delimiter];\n  if (options.delimiter.length === 0) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_DELIMITER\",\n      [\n        \"Invalid option delimiter:\",\n        \"delimiter must be a non empty string or buffer or array of string|buffer,\",\n        `got ${delimiter_json}`,\n      ],\n      options,\n    );\n  }\n  options.delimiter = options.delimiter.map(function (delimiter) {\n    if (delimiter === undefined || delimiter === null || delimiter === false) {\n      return Buffer.from(\",\", options.encoding);\n    }\n    if (typeof delimiter === \"string\") {\n      delimiter = Buffer.from(delimiter, options.encoding);\n    }\n    if (!isBuffer(delimiter) || delimiter.length === 0) {\n      throw new CsvError(\n        \"CSV_INVALID_OPTION_DELIMITER\",\n        [\n          \"Invalid option delimiter:\",\n          \"delimiter must be a non empty string or buffer or array of string|buffer,\",\n          `got ${delimiter_json}`,\n        ],\n        options,\n      );\n    }\n    return delimiter;\n  });\n  // Normalize option `escape`\n  if (options.escape === undefined || options.escape === true) {\n    options.escape = Buffer.from('\"', options.encoding);\n  } else if (typeof options.escape === \"string\") {\n    options.escape = Buffer.from(options.escape, options.encoding);\n  } else if (options.escape === null || options.escape === false) {\n    options.escape = null;\n  }\n  if (options.escape !== null) {\n    if (!isBuffer(options.escape)) {\n      throw new Error(\n        `Invalid Option: escape must be a buffer, a string or a boolean, got ${JSON.stringify(options.escape)}`,\n      );\n    }\n  }\n  // Normalize option `from`\n  if (options.from === undefined || options.from === null) {\n    options.from = 1;\n  } else {\n    if (typeof options.from === \"string\" && /\\d+/.test(options.from)) {\n      options.from = parseInt(options.from);\n    }\n    if (Number.isInteger(options.from)) {\n      if (options.from < 0) {\n        throw new Error(\n          `Invalid Option: from must be a positive integer, got ${JSON.stringify(opts.from)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: from must be an integer, got ${JSON.stringify(options.from)}`,\n      );\n    }\n  }\n  // Normalize option `from_line`\n  if (options.from_line === undefined || options.from_line === null) {\n    options.from_line = 1;\n  } else {\n    if (\n      typeof options.from_line === \"string\" &&\n      /\\d+/.test(options.from_line)\n    ) {\n      options.from_line = parseInt(options.from_line);\n    }\n    if (Number.isInteger(options.from_line)) {\n      if (options.from_line <= 0) {\n        throw new Error(\n          `Invalid Option: from_line must be a positive integer greater than 0, got ${JSON.stringify(opts.from_line)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: from_line must be an integer, got ${JSON.stringify(opts.from_line)}`,\n      );\n    }\n  }\n  // Normalize options `ignore_last_delimiters`\n  if (\n    options.ignore_last_delimiters === undefined ||\n    options.ignore_last_delimiters === null\n  ) {\n    options.ignore_last_delimiters = false;\n  } else if (typeof options.ignore_last_delimiters === \"number\") {\n    options.ignore_last_delimiters = Math.floor(options.ignore_last_delimiters);\n    if (options.ignore_last_delimiters === 0) {\n      options.ignore_last_delimiters = false;\n    }\n  } else if (typeof options.ignore_last_delimiters !== \"boolean\") {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_IGNORE_LAST_DELIMITERS\",\n      [\n        \"Invalid option `ignore_last_delimiters`:\",\n        \"the value must be a boolean value or an integer,\",\n        `got ${JSON.stringify(options.ignore_last_delimiters)}`,\n      ],\n      options,\n    );\n  }\n  if (options.ignore_last_delimiters === true && options.columns === false) {\n    throw new CsvError(\n      \"CSV_IGNORE_LAST_DELIMITERS_REQUIRES_COLUMNS\",\n      [\n        \"The option `ignore_last_delimiters`\",\n        \"requires the activation of the `columns` option\",\n      ],\n      options,\n    );\n  }\n  // Normalize option `info`\n  if (\n    options.info === undefined ||\n    options.info === null ||\n    options.info === false\n  ) {\n    options.info = false;\n  } else if (options.info !== true) {\n    throw new Error(\n      `Invalid Option: info must be true, got ${JSON.stringify(options.info)}`,\n    );\n  }\n  // Normalize option `max_record_size`\n  if (\n    options.max_record_size === undefined ||\n    options.max_record_size === null ||\n    options.max_record_size === false\n  ) {\n    options.max_record_size = 0;\n  } else if (\n    Number.isInteger(options.max_record_size) &&\n    options.max_record_size >= 0\n  ) ; else if (\n    typeof options.max_record_size === \"string\" &&\n    /\\d+/.test(options.max_record_size)\n  ) {\n    options.max_record_size = parseInt(options.max_record_size);\n  } else {\n    throw new Error(\n      `Invalid Option: max_record_size must be a positive integer, got ${JSON.stringify(options.max_record_size)}`,\n    );\n  }\n  // Normalize option `objname`\n  if (\n    options.objname === undefined ||\n    options.objname === null ||\n    options.objname === false\n  ) {\n    options.objname = undefined;\n  } else if (isBuffer(options.objname)) {\n    if (options.objname.length === 0) {\n      throw new Error(`Invalid Option: objname must be a non empty buffer`);\n    }\n    if (options.encoding === null) ; else {\n      options.objname = options.objname.toString(options.encoding);\n    }\n  } else if (typeof options.objname === \"string\") {\n    if (options.objname.length === 0) {\n      throw new Error(`Invalid Option: objname must be a non empty string`);\n    }\n    // Great, nothing to do\n  } else if (typeof options.objname === \"number\") ; else {\n    throw new Error(\n      `Invalid Option: objname must be a string or a buffer, got ${options.objname}`,\n    );\n  }\n  if (options.objname !== undefined) {\n    if (typeof options.objname === \"number\") {\n      if (options.columns !== false) {\n        throw Error(\n          \"Invalid Option: objname index cannot be combined with columns or be defined as a field\",\n        );\n      }\n    } else {\n      // A string or a buffer\n      if (options.columns === false) {\n        throw Error(\n          \"Invalid Option: objname field must be combined with columns or be defined as an index\",\n        );\n      }\n    }\n  }\n  // Normalize option `on_record`\n  if (options.on_record === undefined || options.on_record === null) {\n    options.on_record = undefined;\n  } else if (typeof options.on_record !== \"function\") {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_ON_RECORD\",\n      [\n        \"Invalid option `on_record`:\",\n        \"expect a function,\",\n        `got ${JSON.stringify(options.on_record)}`,\n      ],\n      options,\n    );\n  }\n  // Normalize option `on_skip`\n  // options.on_skip ??= (err, chunk) => {\n  //   this.emit('skip', err, chunk);\n  // };\n  if (\n    options.on_skip !== undefined &&\n    options.on_skip !== null &&\n    typeof options.on_skip !== \"function\"\n  ) {\n    throw new Error(\n      `Invalid Option: on_skip must be a function, got ${JSON.stringify(options.on_skip)}`,\n    );\n  }\n  // Normalize option `quote`\n  if (\n    options.quote === null ||\n    options.quote === false ||\n    options.quote === \"\"\n  ) {\n    options.quote = null;\n  } else {\n    if (options.quote === undefined || options.quote === true) {\n      options.quote = Buffer.from('\"', options.encoding);\n    } else if (typeof options.quote === \"string\") {\n      options.quote = Buffer.from(options.quote, options.encoding);\n    }\n    if (!isBuffer(options.quote)) {\n      throw new Error(\n        `Invalid Option: quote must be a buffer or a string, got ${JSON.stringify(options.quote)}`,\n      );\n    }\n  }\n  // Normalize option `raw`\n  if (\n    options.raw === undefined ||\n    options.raw === null ||\n    options.raw === false\n  ) {\n    options.raw = false;\n  } else if (options.raw !== true) {\n    throw new Error(\n      `Invalid Option: raw must be true, got ${JSON.stringify(options.raw)}`,\n    );\n  }\n  // Normalize option `record_delimiter`\n  if (options.record_delimiter === undefined) {\n    options.record_delimiter = [];\n  } else if (\n    typeof options.record_delimiter === \"string\" ||\n    isBuffer(options.record_delimiter)\n  ) {\n    if (options.record_delimiter.length === 0) {\n      throw new CsvError(\n        \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n        [\n          \"Invalid option `record_delimiter`:\",\n          \"value must be a non empty string or buffer,\",\n          `got ${JSON.stringify(options.record_delimiter)}`,\n        ],\n        options,\n      );\n    }\n    options.record_delimiter = [options.record_delimiter];\n  } else if (!Array.isArray(options.record_delimiter)) {\n    throw new CsvError(\n      \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n      [\n        \"Invalid option `record_delimiter`:\",\n        \"value must be a string, a buffer or array of string|buffer,\",\n        `got ${JSON.stringify(options.record_delimiter)}`,\n      ],\n      options,\n    );\n  }\n  options.record_delimiter = options.record_delimiter.map(function (rd, i) {\n    if (typeof rd !== \"string\" && !isBuffer(rd)) {\n      throw new CsvError(\n        \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n        [\n          \"Invalid option `record_delimiter`:\",\n          \"value must be a string, a buffer or array of string|buffer\",\n          `at index ${i},`,\n          `got ${JSON.stringify(rd)}`,\n        ],\n        options,\n      );\n    } else if (rd.length === 0) {\n      throw new CsvError(\n        \"CSV_INVALID_OPTION_RECORD_DELIMITER\",\n        [\n          \"Invalid option `record_delimiter`:\",\n          \"value must be a non empty string or buffer\",\n          `at index ${i},`,\n          `got ${JSON.stringify(rd)}`,\n        ],\n        options,\n      );\n    }\n    if (typeof rd === \"string\") {\n      rd = Buffer.from(rd, options.encoding);\n    }\n    return rd;\n  });\n  // Normalize option `relax_column_count`\n  if (typeof options.relax_column_count === \"boolean\") ; else if (\n    options.relax_column_count === undefined ||\n    options.relax_column_count === null\n  ) {\n    options.relax_column_count = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_column_count must be a boolean, got ${JSON.stringify(options.relax_column_count)}`,\n    );\n  }\n  if (typeof options.relax_column_count_less === \"boolean\") ; else if (\n    options.relax_column_count_less === undefined ||\n    options.relax_column_count_less === null\n  ) {\n    options.relax_column_count_less = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_column_count_less must be a boolean, got ${JSON.stringify(options.relax_column_count_less)}`,\n    );\n  }\n  if (typeof options.relax_column_count_more === \"boolean\") ; else if (\n    options.relax_column_count_more === undefined ||\n    options.relax_column_count_more === null\n  ) {\n    options.relax_column_count_more = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_column_count_more must be a boolean, got ${JSON.stringify(options.relax_column_count_more)}`,\n    );\n  }\n  // Normalize option `relax_quotes`\n  if (typeof options.relax_quotes === \"boolean\") ; else if (\n    options.relax_quotes === undefined ||\n    options.relax_quotes === null\n  ) {\n    options.relax_quotes = false;\n  } else {\n    throw new Error(\n      `Invalid Option: relax_quotes must be a boolean, got ${JSON.stringify(options.relax_quotes)}`,\n    );\n  }\n  // Normalize option `skip_empty_lines`\n  if (typeof options.skip_empty_lines === \"boolean\") ; else if (\n    options.skip_empty_lines === undefined ||\n    options.skip_empty_lines === null\n  ) {\n    options.skip_empty_lines = false;\n  } else {\n    throw new Error(\n      `Invalid Option: skip_empty_lines must be a boolean, got ${JSON.stringify(options.skip_empty_lines)}`,\n    );\n  }\n  // Normalize option `skip_records_with_empty_values`\n  if (typeof options.skip_records_with_empty_values === \"boolean\") ; else if (\n    options.skip_records_with_empty_values === undefined ||\n    options.skip_records_with_empty_values === null\n  ) {\n    options.skip_records_with_empty_values = false;\n  } else {\n    throw new Error(\n      `Invalid Option: skip_records_with_empty_values must be a boolean, got ${JSON.stringify(options.skip_records_with_empty_values)}`,\n    );\n  }\n  // Normalize option `skip_records_with_error`\n  if (typeof options.skip_records_with_error === \"boolean\") ; else if (\n    options.skip_records_with_error === undefined ||\n    options.skip_records_with_error === null\n  ) {\n    options.skip_records_with_error = false;\n  } else {\n    throw new Error(\n      `Invalid Option: skip_records_with_error must be a boolean, got ${JSON.stringify(options.skip_records_with_error)}`,\n    );\n  }\n  // Normalize option `rtrim`\n  if (\n    options.rtrim === undefined ||\n    options.rtrim === null ||\n    options.rtrim === false\n  ) {\n    options.rtrim = false;\n  } else if (options.rtrim !== true) {\n    throw new Error(\n      `Invalid Option: rtrim must be a boolean, got ${JSON.stringify(options.rtrim)}`,\n    );\n  }\n  // Normalize option `ltrim`\n  if (\n    options.ltrim === undefined ||\n    options.ltrim === null ||\n    options.ltrim === false\n  ) {\n    options.ltrim = false;\n  } else if (options.ltrim !== true) {\n    throw new Error(\n      `Invalid Option: ltrim must be a boolean, got ${JSON.stringify(options.ltrim)}`,\n    );\n  }\n  // Normalize option `trim`\n  if (\n    options.trim === undefined ||\n    options.trim === null ||\n    options.trim === false\n  ) {\n    options.trim = false;\n  } else if (options.trim !== true) {\n    throw new Error(\n      `Invalid Option: trim must be a boolean, got ${JSON.stringify(options.trim)}`,\n    );\n  }\n  // Normalize options `trim`, `ltrim` and `rtrim`\n  if (options.trim === true && opts.ltrim !== false) {\n    options.ltrim = true;\n  } else if (options.ltrim !== true) {\n    options.ltrim = false;\n  }\n  if (options.trim === true && opts.rtrim !== false) {\n    options.rtrim = true;\n  } else if (options.rtrim !== true) {\n    options.rtrim = false;\n  }\n  // Normalize option `to`\n  if (options.to === undefined || options.to === null) {\n    options.to = -1;\n  } else {\n    if (typeof options.to === \"string\" && /\\d+/.test(options.to)) {\n      options.to = parseInt(options.to);\n    }\n    if (Number.isInteger(options.to)) {\n      if (options.to <= 0) {\n        throw new Error(\n          `Invalid Option: to must be a positive integer greater than 0, got ${JSON.stringify(opts.to)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: to must be an integer, got ${JSON.stringify(opts.to)}`,\n      );\n    }\n  }\n  // Normalize option `to_line`\n  if (options.to_line === undefined || options.to_line === null) {\n    options.to_line = -1;\n  } else {\n    if (typeof options.to_line === \"string\" && /\\d+/.test(options.to_line)) {\n      options.to_line = parseInt(options.to_line);\n    }\n    if (Number.isInteger(options.to_line)) {\n      if (options.to_line <= 0) {\n        throw new Error(\n          `Invalid Option: to_line must be a positive integer greater than 0, got ${JSON.stringify(opts.to_line)}`,\n        );\n      }\n    } else {\n      throw new Error(\n        `Invalid Option: to_line must be an integer, got ${JSON.stringify(opts.to_line)}`,\n      );\n    }\n  }\n  return options;\n};\n\nconst isRecordEmpty = function (record) {\n  return record.every(\n    (field) =>\n      field == null || (field.toString && field.toString().trim() === \"\"),\n  );\n};\n\nconst cr = 13; // `\\r`, carriage return, 0x0D in hexadécimal, 13 in decimal\nconst nl = 10; // `\\n`, newline, 0x0A in hexadecimal, 10 in decimal\n\nconst boms = {\n  // Note, the following are equals:\n  // Buffer.from(\"\\ufeff\")\n  // Buffer.from([239, 187, 191])\n  // Buffer.from('EFBBBF', 'hex')\n  utf8: Buffer.from([239, 187, 191]),\n  // Note, the following are equals:\n  // Buffer.from \"\\ufeff\", 'utf16le\n  // Buffer.from([255, 254])\n  utf16le: Buffer.from([255, 254]),\n};\n\nconst transform = function (original_options = {}) {\n  const info = {\n    bytes: 0,\n    comment_lines: 0,\n    empty_lines: 0,\n    invalid_field_length: 0,\n    lines: 1,\n    records: 0,\n  };\n  const options = normalize_options(original_options);\n  return {\n    info: info,\n    original_options: original_options,\n    options: options,\n    state: init_state(options),\n    __needMoreData: function (i, bufLen, end) {\n      if (end) return false;\n      const { encoding, escape, quote } = this.options;\n      const { quoting, needMoreDataSize, recordDelimiterMaxLength } =\n        this.state;\n      const numOfCharLeft = bufLen - i - 1;\n      const requiredLength = Math.max(\n        needMoreDataSize,\n        // Skip if the remaining buffer smaller than record delimiter\n        // If \"record_delimiter\" is yet to be discovered:\n        // 1. It is equals to `[]` and \"recordDelimiterMaxLength\" equals `0`\n        // 2. We set the length to windows line ending in the current encoding\n        // Note, that encoding is known from user or bom discovery at that point\n        // recordDelimiterMaxLength,\n        recordDelimiterMaxLength === 0\n          ? Buffer.from(\"\\r\\n\", encoding).length\n          : recordDelimiterMaxLength,\n        // Skip if remaining buffer can be an escaped quote\n        quoting ? (escape === null ? 0 : escape.length) + quote.length : 0,\n        // Skip if remaining buffer can be record delimiter following the closing quote\n        quoting ? quote.length + recordDelimiterMaxLength : 0,\n      );\n      return numOfCharLeft < requiredLength;\n    },\n    // Central parser implementation\n    parse: function (nextBuf, end, push, close) {\n      const {\n        bom,\n        comment_no_infix,\n        encoding,\n        from_line,\n        ltrim,\n        max_record_size,\n        raw,\n        relax_quotes,\n        rtrim,\n        skip_empty_lines,\n        to,\n        to_line,\n      } = this.options;\n      let { comment, escape, quote, record_delimiter } = this.options;\n      const { bomSkipped, previousBuf, rawBuffer, escapeIsQuote } = this.state;\n      let buf;\n      if (previousBuf === undefined) {\n        if (nextBuf === undefined) {\n          // Handle empty string\n          close();\n          return;\n        } else {\n          buf = nextBuf;\n        }\n      } else if (previousBuf !== undefined && nextBuf === undefined) {\n        buf = previousBuf;\n      } else {\n        buf = Buffer.concat([previousBuf, nextBuf]);\n      }\n      // Handle UTF BOM\n      if (bomSkipped === false) {\n        if (bom === false) {\n          this.state.bomSkipped = true;\n        } else if (buf.length < 3) {\n          // No enough data\n          if (end === false) {\n            // Wait for more data\n            this.state.previousBuf = buf;\n            return;\n          }\n        } else {\n          for (const encoding in boms) {\n            if (boms[encoding].compare(buf, 0, boms[encoding].length) === 0) {\n              // Skip BOM\n              const bomLength = boms[encoding].length;\n              this.state.bufBytesStart += bomLength;\n              buf = buf.slice(bomLength);\n              // Renormalize original options with the new encoding\n              this.options = normalize_options({\n                ...this.original_options,\n                encoding: encoding,\n              });\n              // Options will re-evaluate the Buffer with the new encoding\n              ({ comment, escape, quote } = this.options);\n              break;\n            }\n          }\n          this.state.bomSkipped = true;\n        }\n      }\n      const bufLen = buf.length;\n      let pos;\n      for (pos = 0; pos < bufLen; pos++) {\n        // Ensure we get enough space to look ahead\n        // There should be a way to move this out of the loop\n        if (this.__needMoreData(pos, bufLen, end)) {\n          break;\n        }\n        if (this.state.wasRowDelimiter === true) {\n          this.info.lines++;\n          this.state.wasRowDelimiter = false;\n        }\n        if (to_line !== -1 && this.info.lines > to_line) {\n          this.state.stop = true;\n          close();\n          return;\n        }\n        // Auto discovery of record_delimiter, unix, mac and windows supported\n        if (this.state.quoting === false && record_delimiter.length === 0) {\n          const record_delimiterCount = this.__autoDiscoverRecordDelimiter(\n            buf,\n            pos,\n          );\n          if (record_delimiterCount) {\n            record_delimiter = this.options.record_delimiter;\n          }\n        }\n        const chr = buf[pos];\n        if (raw === true) {\n          rawBuffer.append(chr);\n        }\n        if (\n          (chr === cr || chr === nl) &&\n          this.state.wasRowDelimiter === false\n        ) {\n          this.state.wasRowDelimiter = true;\n        }\n        // Previous char was a valid escape char\n        // treat the current char as a regular char\n        if (this.state.escaping === true) {\n          this.state.escaping = false;\n        } else {\n          // Escape is only active inside quoted fields\n          // We are quoting, the char is an escape chr and there is a chr to escape\n          // if(escape !== null && this.state.quoting === true && chr === escape && pos + 1 < bufLen){\n          if (\n            escape !== null &&\n            this.state.quoting === true &&\n            this.__isEscape(buf, pos, chr) &&\n            pos + escape.length < bufLen\n          ) {\n            if (escapeIsQuote) {\n              if (this.__isQuote(buf, pos + escape.length)) {\n                this.state.escaping = true;\n                pos += escape.length - 1;\n                continue;\n              }\n            } else {\n              this.state.escaping = true;\n              pos += escape.length - 1;\n              continue;\n            }\n          }\n          // Not currently escaping and chr is a quote\n          // TODO: need to compare bytes instead of single char\n          if (this.state.commenting === false && this.__isQuote(buf, pos)) {\n            if (this.state.quoting === true) {\n              const nextChr = buf[pos + quote.length];\n              const isNextChrTrimable =\n                rtrim && this.__isCharTrimable(buf, pos + quote.length);\n              const isNextChrComment =\n                comment !== null &&\n                this.__compareBytes(comment, buf, pos + quote.length, nextChr);\n              const isNextChrDelimiter = this.__isDelimiter(\n                buf,\n                pos + quote.length,\n                nextChr,\n              );\n              const isNextChrRecordDelimiter =\n                record_delimiter.length === 0\n                  ? this.__autoDiscoverRecordDelimiter(buf, pos + quote.length)\n                  : this.__isRecordDelimiter(nextChr, buf, pos + quote.length);\n              // Escape a quote\n              // Treat next char as a regular character\n              if (\n                escape !== null &&\n                this.__isEscape(buf, pos, chr) &&\n                this.__isQuote(buf, pos + escape.length)\n              ) {\n                pos += escape.length - 1;\n              } else if (\n                !nextChr ||\n                isNextChrDelimiter ||\n                isNextChrRecordDelimiter ||\n                isNextChrComment ||\n                isNextChrTrimable\n              ) {\n                this.state.quoting = false;\n                this.state.wasQuoting = true;\n                pos += quote.length - 1;\n                continue;\n              } else if (relax_quotes === false) {\n                const err = this.__error(\n                  new CsvError(\n                    \"CSV_INVALID_CLOSING_QUOTE\",\n                    [\n                      \"Invalid Closing Quote:\",\n                      `got \"${String.fromCharCode(nextChr)}\"`,\n                      `at line ${this.info.lines}`,\n                      \"instead of delimiter, record delimiter, trimable character\",\n                      \"(if activated) or comment\",\n                    ],\n                    this.options,\n                    this.__infoField(),\n                  ),\n                );\n                if (err !== undefined) return err;\n              } else {\n                this.state.quoting = false;\n                this.state.wasQuoting = true;\n                this.state.field.prepend(quote);\n                pos += quote.length - 1;\n              }\n            } else {\n              if (this.state.field.length !== 0) {\n                // In relax_quotes mode, treat opening quote preceded by chrs as regular\n                if (relax_quotes === false) {\n                  const info = this.__infoField();\n                  const bom = Object.keys(boms)\n                    .map((b) =>\n                      boms[b].equals(this.state.field.toString()) ? b : false,\n                    )\n                    .filter(Boolean)[0];\n                  const err = this.__error(\n                    new CsvError(\n                      \"INVALID_OPENING_QUOTE\",\n                      [\n                        \"Invalid Opening Quote:\",\n                        `a quote is found on field ${JSON.stringify(info.column)} at line ${info.lines}, value is ${JSON.stringify(this.state.field.toString(encoding))}`,\n                        bom ? `(${bom} bom)` : undefined,\n                      ],\n                      this.options,\n                      info,\n                      {\n                        field: this.state.field,\n                      },\n                    ),\n                  );\n                  if (err !== undefined) return err;\n                }\n              } else {\n                this.state.quoting = true;\n                pos += quote.length - 1;\n                continue;\n              }\n            }\n          }\n          if (this.state.quoting === false) {\n            const recordDelimiterLength = this.__isRecordDelimiter(\n              chr,\n              buf,\n              pos,\n            );\n            if (recordDelimiterLength !== 0) {\n              // Do not emit comments which take a full line\n              const skipCommentLine =\n                this.state.commenting &&\n                this.state.wasQuoting === false &&\n                this.state.record.length === 0 &&\n                this.state.field.length === 0;\n              if (skipCommentLine) {\n                this.info.comment_lines++;\n                // Skip full comment line\n              } else {\n                // Activate records emition if above from_line\n                if (\n                  this.state.enabled === false &&\n                  this.info.lines +\n                    (this.state.wasRowDelimiter === true ? 1 : 0) >=\n                    from_line\n                ) {\n                  this.state.enabled = true;\n                  this.__resetField();\n                  this.__resetRecord();\n                  pos += recordDelimiterLength - 1;\n                  continue;\n                }\n                // Skip if line is empty and skip_empty_lines activated\n                if (\n                  skip_empty_lines === true &&\n                  this.state.wasQuoting === false &&\n                  this.state.record.length === 0 &&\n                  this.state.field.length === 0\n                ) {\n                  this.info.empty_lines++;\n                  pos += recordDelimiterLength - 1;\n                  continue;\n                }\n                this.info.bytes = this.state.bufBytesStart + pos;\n                const errField = this.__onField();\n                if (errField !== undefined) return errField;\n                this.info.bytes =\n                  this.state.bufBytesStart + pos + recordDelimiterLength;\n                const errRecord = this.__onRecord(push);\n                if (errRecord !== undefined) return errRecord;\n                if (to !== -1 && this.info.records >= to) {\n                  this.state.stop = true;\n                  close();\n                  return;\n                }\n              }\n              this.state.commenting = false;\n              pos += recordDelimiterLength - 1;\n              continue;\n            }\n            if (this.state.commenting) {\n              continue;\n            }\n            if (\n              comment !== null &&\n              (comment_no_infix === false ||\n                (this.state.record.length === 0 &&\n                  this.state.field.length === 0))\n            ) {\n              const commentCount = this.__compareBytes(comment, buf, pos, chr);\n              if (commentCount !== 0) {\n                this.state.commenting = true;\n                continue;\n              }\n            }\n            const delimiterLength = this.__isDelimiter(buf, pos, chr);\n            if (delimiterLength !== 0) {\n              this.info.bytes = this.state.bufBytesStart + pos;\n              const errField = this.__onField();\n              if (errField !== undefined) return errField;\n              pos += delimiterLength - 1;\n              continue;\n            }\n          }\n        }\n        if (this.state.commenting === false) {\n          if (\n            max_record_size !== 0 &&\n            this.state.record_length + this.state.field.length > max_record_size\n          ) {\n            return this.__error(\n              new CsvError(\n                \"CSV_MAX_RECORD_SIZE\",\n                [\n                  \"Max Record Size:\",\n                  \"record exceed the maximum number of tolerated bytes\",\n                  `of ${max_record_size}`,\n                  `at line ${this.info.lines}`,\n                ],\n                this.options,\n                this.__infoField(),\n              ),\n            );\n          }\n        }\n        const lappend =\n          ltrim === false ||\n          this.state.quoting === true ||\n          this.state.field.length !== 0 ||\n          !this.__isCharTrimable(buf, pos);\n        // rtrim in non quoting is handle in __onField\n        const rappend = rtrim === false || this.state.wasQuoting === false;\n        if (lappend === true && rappend === true) {\n          this.state.field.append(chr);\n        } else if (rtrim === true && !this.__isCharTrimable(buf, pos)) {\n          return this.__error(\n            new CsvError(\n              \"CSV_NON_TRIMABLE_CHAR_AFTER_CLOSING_QUOTE\",\n              [\n                \"Invalid Closing Quote:\",\n                \"found non trimable byte after quote\",\n                `at line ${this.info.lines}`,\n              ],\n              this.options,\n              this.__infoField(),\n            ),\n          );\n        } else {\n          if (lappend === false) {\n            pos += this.__isCharTrimable(buf, pos) - 1;\n          }\n          continue;\n        }\n      }\n      if (end === true) {\n        // Ensure we are not ending in a quoting state\n        if (this.state.quoting === true) {\n          const err = this.__error(\n            new CsvError(\n              \"CSV_QUOTE_NOT_CLOSED\",\n              [\n                \"Quote Not Closed:\",\n                `the parsing is finished with an opening quote at line ${this.info.lines}`,\n              ],\n              this.options,\n              this.__infoField(),\n            ),\n          );\n          if (err !== undefined) return err;\n        } else {\n          // Skip last line if it has no characters\n          if (\n            this.state.wasQuoting === true ||\n            this.state.record.length !== 0 ||\n            this.state.field.length !== 0\n          ) {\n            this.info.bytes = this.state.bufBytesStart + pos;\n            const errField = this.__onField();\n            if (errField !== undefined) return errField;\n            const errRecord = this.__onRecord(push);\n            if (errRecord !== undefined) return errRecord;\n          } else if (this.state.wasRowDelimiter === true) {\n            this.info.empty_lines++;\n          } else if (this.state.commenting === true) {\n            this.info.comment_lines++;\n          }\n        }\n      } else {\n        this.state.bufBytesStart += pos;\n        this.state.previousBuf = buf.slice(pos);\n      }\n      if (this.state.wasRowDelimiter === true) {\n        this.info.lines++;\n        this.state.wasRowDelimiter = false;\n      }\n    },\n    __onRecord: function (push) {\n      const {\n        columns,\n        group_columns_by_name,\n        encoding,\n        info,\n        from,\n        relax_column_count,\n        relax_column_count_less,\n        relax_column_count_more,\n        raw,\n        skip_records_with_empty_values,\n      } = this.options;\n      const { enabled, record } = this.state;\n      if (enabled === false) {\n        return this.__resetRecord();\n      }\n      // Convert the first line into column names\n      const recordLength = record.length;\n      if (columns === true) {\n        if (skip_records_with_empty_values === true && isRecordEmpty(record)) {\n          this.__resetRecord();\n          return;\n        }\n        return this.__firstLineToColumns(record);\n      }\n      if (columns === false && this.info.records === 0) {\n        this.state.expectedRecordLength = recordLength;\n      }\n      if (recordLength !== this.state.expectedRecordLength) {\n        const err =\n          columns === false\n            ? new CsvError(\n                \"CSV_RECORD_INCONSISTENT_FIELDS_LENGTH\",\n                [\n                  \"Invalid Record Length:\",\n                  `expect ${this.state.expectedRecordLength},`,\n                  `got ${recordLength} on line ${this.info.lines}`,\n                ],\n                this.options,\n                this.__infoField(),\n                {\n                  record: record,\n                },\n              )\n            : new CsvError(\n                \"CSV_RECORD_INCONSISTENT_COLUMNS\",\n                [\n                  \"Invalid Record Length:\",\n                  `columns length is ${columns.length},`, // rename columns\n                  `got ${recordLength} on line ${this.info.lines}`,\n                ],\n                this.options,\n                this.__infoField(),\n                {\n                  record: record,\n                },\n              );\n        if (\n          relax_column_count === true ||\n          (relax_column_count_less === true &&\n            recordLength < this.state.expectedRecordLength) ||\n          (relax_column_count_more === true &&\n            recordLength > this.state.expectedRecordLength)\n        ) {\n          this.info.invalid_field_length++;\n          this.state.error = err;\n          // Error is undefined with skip_records_with_error\n        } else {\n          const finalErr = this.__error(err);\n          if (finalErr) return finalErr;\n        }\n      }\n      if (skip_records_with_empty_values === true && isRecordEmpty(record)) {\n        this.__resetRecord();\n        return;\n      }\n      if (this.state.recordHasError === true) {\n        this.__resetRecord();\n        this.state.recordHasError = false;\n        return;\n      }\n      this.info.records++;\n      if (from === 1 || this.info.records >= from) {\n        const { objname } = this.options;\n        // With columns, records are object\n        if (columns !== false) {\n          const obj = {};\n          // Transform record array to an object\n          for (let i = 0, l = record.length; i < l; i++) {\n            if (columns[i] === undefined || columns[i].disabled) continue;\n            // Turn duplicate columns into an array\n            if (\n              group_columns_by_name === true &&\n              obj[columns[i].name] !== undefined\n            ) {\n              if (Array.isArray(obj[columns[i].name])) {\n                obj[columns[i].name] = obj[columns[i].name].concat(record[i]);\n              } else {\n                obj[columns[i].name] = [obj[columns[i].name], record[i]];\n              }\n            } else {\n              obj[columns[i].name] = record[i];\n            }\n          }\n          // Without objname (default)\n          if (raw === true || info === true) {\n            const extRecord = Object.assign(\n              { record: obj },\n              raw === true\n                ? { raw: this.state.rawBuffer.toString(encoding) }\n                : {},\n              info === true ? { info: this.__infoRecord() } : {},\n            );\n            const err = this.__push(\n              objname === undefined ? extRecord : [obj[objname], extRecord],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          } else {\n            const err = this.__push(\n              objname === undefined ? obj : [obj[objname], obj],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          }\n          // Without columns, records are array\n        } else {\n          if (raw === true || info === true) {\n            const extRecord = Object.assign(\n              { record: record },\n              raw === true\n                ? { raw: this.state.rawBuffer.toString(encoding) }\n                : {},\n              info === true ? { info: this.__infoRecord() } : {},\n            );\n            const err = this.__push(\n              objname === undefined ? extRecord : [record[objname], extRecord],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          } else {\n            const err = this.__push(\n              objname === undefined ? record : [record[objname], record],\n              push,\n            );\n            if (err) {\n              return err;\n            }\n          }\n        }\n      }\n      this.__resetRecord();\n    },\n    __firstLineToColumns: function (record) {\n      const { firstLineToHeaders } = this.state;\n      try {\n        const headers =\n          firstLineToHeaders === undefined\n            ? record\n            : firstLineToHeaders.call(null, record);\n        if (!Array.isArray(headers)) {\n          return this.__error(\n            new CsvError(\n              \"CSV_INVALID_COLUMN_MAPPING\",\n              [\n                \"Invalid Column Mapping:\",\n                \"expect an array from column function,\",\n                `got ${JSON.stringify(headers)}`,\n              ],\n              this.options,\n              this.__infoField(),\n              {\n                headers: headers,\n              },\n            ),\n          );\n        }\n        const normalizedHeaders = normalize_columns_array(headers);\n        this.state.expectedRecordLength = normalizedHeaders.length;\n        this.options.columns = normalizedHeaders;\n        this.__resetRecord();\n        return;\n      } catch (err) {\n        return err;\n      }\n    },\n    __resetRecord: function () {\n      if (this.options.raw === true) {\n        this.state.rawBuffer.reset();\n      }\n      this.state.error = undefined;\n      this.state.record = [];\n      this.state.record_length = 0;\n    },\n    __onField: function () {\n      const { cast, encoding, rtrim, max_record_size } = this.options;\n      const { enabled, wasQuoting } = this.state;\n      // Short circuit for the from_line options\n      if (enabled === false) {\n        return this.__resetField();\n      }\n      let field = this.state.field.toString(encoding);\n      if (rtrim === true && wasQuoting === false) {\n        field = field.trimRight();\n      }\n      if (cast === true) {\n        const [err, f] = this.__cast(field);\n        if (err !== undefined) return err;\n        field = f;\n      }\n      this.state.record.push(field);\n      // Increment record length if record size must not exceed a limit\n      if (max_record_size !== 0 && typeof field === \"string\") {\n        this.state.record_length += field.length;\n      }\n      this.__resetField();\n    },\n    __resetField: function () {\n      this.state.field.reset();\n      this.state.wasQuoting = false;\n    },\n    __push: function (record, push) {\n      const { on_record } = this.options;\n      if (on_record !== undefined) {\n        const info = this.__infoRecord();\n        try {\n          record = on_record.call(null, record, info);\n        } catch (err) {\n          return err;\n        }\n        if (record === undefined || record === null) {\n          return;\n        }\n      }\n      push(record);\n    },\n    // Return a tuple with the error and the casted value\n    __cast: function (field) {\n      const { columns, relax_column_count } = this.options;\n      const isColumns = Array.isArray(columns);\n      // Dont loose time calling cast\n      // because the final record is an object\n      // and this field can't be associated to a key present in columns\n      if (\n        isColumns === true &&\n        relax_column_count &&\n        this.options.columns.length <= this.state.record.length\n      ) {\n        return [undefined, undefined];\n      }\n      if (this.state.castField !== null) {\n        try {\n          const info = this.__infoField();\n          return [undefined, this.state.castField.call(null, field, info)];\n        } catch (err) {\n          return [err];\n        }\n      }\n      if (this.__isFloat(field)) {\n        return [undefined, parseFloat(field)];\n      } else if (this.options.cast_date !== false) {\n        const info = this.__infoField();\n        return [undefined, this.options.cast_date.call(null, field, info)];\n      }\n      return [undefined, field];\n    },\n    // Helper to test if a character is a space or a line delimiter\n    __isCharTrimable: function (buf, pos) {\n      const isTrim = (buf, pos) => {\n        const { timchars } = this.state;\n        loop1: for (let i = 0; i < timchars.length; i++) {\n          const timchar = timchars[i];\n          for (let j = 0; j < timchar.length; j++) {\n            if (timchar[j] !== buf[pos + j]) continue loop1;\n          }\n          return timchar.length;\n        }\n        return 0;\n      };\n      return isTrim(buf, pos);\n    },\n    // Keep it in case we implement the `cast_int` option\n    // __isInt(value){\n    //   // return Number.isInteger(parseInt(value))\n    //   // return !isNaN( parseInt( obj ) );\n    //   return /^(\\-|\\+)?[1-9][0-9]*$/.test(value)\n    // }\n    __isFloat: function (value) {\n      return value - parseFloat(value) + 1 >= 0; // Borrowed from jquery\n    },\n    __compareBytes: function (sourceBuf, targetBuf, targetPos, firstByte) {\n      if (sourceBuf[0] !== firstByte) return 0;\n      const sourceLength = sourceBuf.length;\n      for (let i = 1; i < sourceLength; i++) {\n        if (sourceBuf[i] !== targetBuf[targetPos + i]) return 0;\n      }\n      return sourceLength;\n    },\n    __isDelimiter: function (buf, pos, chr) {\n      const { delimiter, ignore_last_delimiters } = this.options;\n      if (\n        ignore_last_delimiters === true &&\n        this.state.record.length === this.options.columns.length - 1\n      ) {\n        return 0;\n      } else if (\n        ignore_last_delimiters !== false &&\n        typeof ignore_last_delimiters === \"number\" &&\n        this.state.record.length === ignore_last_delimiters - 1\n      ) {\n        return 0;\n      }\n      loop1: for (let i = 0; i < delimiter.length; i++) {\n        const del = delimiter[i];\n        if (del[0] === chr) {\n          for (let j = 1; j < del.length; j++) {\n            if (del[j] !== buf[pos + j]) continue loop1;\n          }\n          return del.length;\n        }\n      }\n      return 0;\n    },\n    __isRecordDelimiter: function (chr, buf, pos) {\n      const { record_delimiter } = this.options;\n      const recordDelimiterLength = record_delimiter.length;\n      loop1: for (let i = 0; i < recordDelimiterLength; i++) {\n        const rd = record_delimiter[i];\n        const rdLength = rd.length;\n        if (rd[0] !== chr) {\n          continue;\n        }\n        for (let j = 1; j < rdLength; j++) {\n          if (rd[j] !== buf[pos + j]) {\n            continue loop1;\n          }\n        }\n        return rd.length;\n      }\n      return 0;\n    },\n    __isEscape: function (buf, pos, chr) {\n      const { escape } = this.options;\n      if (escape === null) return false;\n      const l = escape.length;\n      if (escape[0] === chr) {\n        for (let i = 0; i < l; i++) {\n          if (escape[i] !== buf[pos + i]) {\n            return false;\n          }\n        }\n        return true;\n      }\n      return false;\n    },\n    __isQuote: function (buf, pos) {\n      const { quote } = this.options;\n      if (quote === null) return false;\n      const l = quote.length;\n      for (let i = 0; i < l; i++) {\n        if (quote[i] !== buf[pos + i]) {\n          return false;\n        }\n      }\n      return true;\n    },\n    __autoDiscoverRecordDelimiter: function (buf, pos) {\n      const { encoding } = this.options;\n      // Note, we don't need to cache this information in state,\n      // It is only called on the first line until we find out a suitable\n      // record delimiter.\n      const rds = [\n        // Important, the windows line ending must be before mac os 9\n        Buffer.from(\"\\r\\n\", encoding),\n        Buffer.from(\"\\n\", encoding),\n        Buffer.from(\"\\r\", encoding),\n      ];\n      loop: for (let i = 0; i < rds.length; i++) {\n        const l = rds[i].length;\n        for (let j = 0; j < l; j++) {\n          if (rds[i][j] !== buf[pos + j]) {\n            continue loop;\n          }\n        }\n        this.options.record_delimiter.push(rds[i]);\n        this.state.recordDelimiterMaxLength = rds[i].length;\n        return rds[i].length;\n      }\n      return 0;\n    },\n    __error: function (msg) {\n      const { encoding, raw, skip_records_with_error } = this.options;\n      const err = typeof msg === \"string\" ? new Error(msg) : msg;\n      if (skip_records_with_error) {\n        this.state.recordHasError = true;\n        if (this.options.on_skip !== undefined) {\n          this.options.on_skip(\n            err,\n            raw ? this.state.rawBuffer.toString(encoding) : undefined,\n          );\n        }\n        // this.emit('skip', err, raw ? this.state.rawBuffer.toString(encoding) : undefined);\n        return undefined;\n      } else {\n        return err;\n      }\n    },\n    __infoDataSet: function () {\n      return {\n        ...this.info,\n        columns: this.options.columns,\n      };\n    },\n    __infoRecord: function () {\n      const { columns, raw, encoding } = this.options;\n      return {\n        ...this.__infoDataSet(),\n        error: this.state.error,\n        header: columns === true,\n        index: this.state.record.length,\n        raw: raw ? this.state.rawBuffer.toString(encoding) : undefined,\n      };\n    },\n    __infoField: function () {\n      const { columns } = this.options;\n      const isColumns = Array.isArray(columns);\n      return {\n        ...this.__infoRecord(),\n        column:\n          isColumns === true\n            ? columns.length > this.state.record.length\n              ? columns[this.state.record.length].name\n              : null\n            : this.state.record.length,\n        quoting: this.state.wasQuoting,\n      };\n    },\n  };\n};\n\nconst parse = function (data, opts = {}) {\n  if (typeof data === \"string\") {\n    data = Buffer.from(data);\n  }\n  const records = opts && opts.objname ? {} : [];\n  const parser = transform(opts);\n  const push = (record) => {\n    if (parser.options.objname === undefined) records.push(record);\n    else {\n      records[record[0]] = record[1];\n    }\n  };\n  const close = () => {};\n  const err1 = parser.parse(data, false, push, close);\n  if (err1 !== undefined) throw err1;\n  const err2 = parser.parse(undefined, true, push, close);\n  if (err2 !== undefined) throw err2;\n  return records;\n};\n\nexport { CsvError, parse };\n"], "mappings": ";;;AAAA,IAAI,WAAY,OAAO,WAAW,cAAc,SACpC,OAAO,SAAS,cAAc,OAC9B,OAAO,WAAW,cAAc,SAAS,CAAC;AAEtD,IAAI,SAAS,CAAC;AACd,IAAI,YAAY,CAAC;AACjB,IAAI,MAAM,OAAO,eAAe,cAAc,aAAa;AAC3D,IAAI,SAAS;AACb,SAAS,OAAQ;AACf,WAAS;AACT,MAAI,OAAO;AACX,WAAS,IAAI,GAAG,MAAM,KAAK,QAAQ,IAAI,KAAK,EAAE,GAAG;AAC/C,WAAO,CAAC,IAAI,KAAK,CAAC;AAClB,cAAU,KAAK,WAAW,CAAC,CAAC,IAAI;AAAA,EAClC;AAEA,YAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AAC/B,YAAU,IAAI,WAAW,CAAC,CAAC,IAAI;AACjC;AAEA,SAAS,YAAa,KAAK;AACzB,MAAI,CAAC,QAAQ;AACX,SAAK;AAAA,EACP;AACA,MAAI,GAAG,GAAG,GAAG,KAAK,cAAc;AAChC,MAAI,MAAM,IAAI;AAEd,MAAI,MAAM,IAAI,GAAG;AACf,UAAM,IAAI,MAAM,gDAAgD;AAAA,EAClE;AAOA,iBAAe,IAAI,MAAM,CAAC,MAAM,MAAM,IAAI,IAAI,MAAM,CAAC,MAAM,MAAM,IAAI;AAGrE,QAAM,IAAI,IAAI,MAAM,IAAI,IAAI,YAAY;AAGxC,MAAI,eAAe,IAAI,MAAM,IAAI;AAEjC,MAAI,IAAI;AAER,OAAK,IAAI,GAAG,IAAI,GAAG,IAAI,GAAG,KAAK,GAAG,KAAK,GAAG;AACxC,UAAO,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,KAAO,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,KAAO,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,IAAK,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC;AACjK,QAAI,GAAG,IAAK,OAAO,KAAM;AACzB,QAAI,GAAG,IAAK,OAAO,IAAK;AACxB,QAAI,GAAG,IAAI,MAAM;AAAA,EACnB;AAEA,MAAI,iBAAiB,GAAG;AACtB,UAAO,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,IAAM,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK;AACjF,QAAI,GAAG,IAAI,MAAM;AAAA,EACnB,WAAW,iBAAiB,GAAG;AAC7B,UAAO,UAAU,IAAI,WAAW,CAAC,CAAC,KAAK,KAAO,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK,IAAM,UAAU,IAAI,WAAW,IAAI,CAAC,CAAC,KAAK;AAC5H,QAAI,GAAG,IAAK,OAAO,IAAK;AACxB,QAAI,GAAG,IAAI,MAAM;AAAA,EACnB;AAEA,SAAO;AACT;AAEA,SAAS,gBAAiB,KAAK;AAC7B,SAAO,OAAO,OAAO,KAAK,EAAI,IAAI,OAAO,OAAO,KAAK,EAAI,IAAI,OAAO,OAAO,IAAI,EAAI,IAAI,OAAO,MAAM,EAAI;AAC1G;AAEA,SAAS,YAAa,OAAO,OAAO,KAAK;AACvC,MAAI;AACJ,MAAI,SAAS,CAAC;AACd,WAAS,IAAI,OAAO,IAAI,KAAK,KAAK,GAAG;AACnC,WAAO,MAAM,CAAC,KAAK,OAAO,MAAM,IAAI,CAAC,KAAK,KAAM,MAAM,IAAI,CAAC;AAC3D,WAAO,KAAK,gBAAgB,GAAG,CAAC;AAAA,EAClC;AACA,SAAO,OAAO,KAAK,EAAE;AACvB;AAEA,SAAS,cAAe,OAAO;AAC7B,MAAI,CAAC,QAAQ;AACX,SAAK;AAAA,EACP;AACA,MAAI;AACJ,MAAI,MAAM,MAAM;AAChB,MAAI,aAAa,MAAM;AACvB,MAAI,SAAS;AACb,MAAI,QAAQ,CAAC;AACb,MAAI,iBAAiB;AAGrB,WAAS,IAAI,GAAG,OAAO,MAAM,YAAY,IAAI,MAAM,KAAK,gBAAgB;AACtE,UAAM,KAAK,YAAY,OAAO,GAAI,IAAI,iBAAkB,OAAO,OAAQ,IAAI,cAAe,CAAC;AAAA,EAC7F;AAGA,MAAI,eAAe,GAAG;AACpB,UAAM,MAAM,MAAM,CAAC;AACnB,cAAU,OAAO,OAAO,CAAC;AACzB,cAAU,OAAQ,OAAO,IAAK,EAAI;AAClC,cAAU;AAAA,EACZ,WAAW,eAAe,GAAG;AAC3B,WAAO,MAAM,MAAM,CAAC,KAAK,KAAM,MAAM,MAAM,CAAC;AAC5C,cAAU,OAAO,OAAO,EAAE;AAC1B,cAAU,OAAQ,OAAO,IAAK,EAAI;AAClC,cAAU,OAAQ,OAAO,IAAK,EAAI;AAClC,cAAU;AAAA,EACZ;AAEA,QAAM,KAAK,MAAM;AAEjB,SAAO,MAAM,KAAK,EAAE;AACtB;AAEA,SAAS,KAAM,QAAQ,QAAQ,MAAM,MAAM,QAAQ;AACjD,MAAI,GAAG;AACP,MAAI,OAAO,SAAS,IAAI,OAAO;AAC/B,MAAI,QAAQ,KAAK,QAAQ;AACzB,MAAI,QAAQ,QAAQ;AACpB,MAAI,QAAQ;AACZ,MAAI,IAAI,OAAQ,SAAS,IAAK;AAC9B,MAAI,IAAI,OAAO,KAAK;AACpB,MAAI,IAAI,OAAO,SAAS,CAAC;AAEzB,OAAK;AAEL,MAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,QAAO,CAAC;AACR,WAAS;AACT,SAAO,QAAQ,GAAG,IAAI,IAAI,MAAM,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG;AAAA,EAAC;AAEzE,MAAI,KAAM,KAAM,CAAC,SAAU;AAC3B,QAAO,CAAC;AACR,WAAS;AACT,SAAO,QAAQ,GAAG,IAAI,IAAI,MAAM,OAAO,SAAS,CAAC,GAAG,KAAK,GAAG,SAAS,GAAG;AAAA,EAAC;AAEzE,MAAI,MAAM,GAAG;AACX,QAAI,IAAI;AAAA,EACV,WAAW,MAAM,MAAM;AACrB,WAAO,IAAI,OAAQ,IAAI,KAAK,KAAK;AAAA,EACnC,OAAO;AACL,QAAI,IAAI,KAAK,IAAI,GAAG,IAAI;AACxB,QAAI,IAAI;AAAA,EACV;AACA,UAAQ,IAAI,KAAK,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,IAAI;AAChD;AAEA,SAAS,MAAO,QAAQ,OAAO,QAAQ,MAAM,MAAM,QAAQ;AACzD,MAAI,GAAG,GAAG;AACV,MAAI,OAAO,SAAS,IAAI,OAAO;AAC/B,MAAI,QAAQ,KAAK,QAAQ;AACzB,MAAI,QAAQ,QAAQ;AACpB,MAAI,KAAM,SAAS,KAAK,KAAK,IAAI,GAAG,GAAG,IAAI,KAAK,IAAI,GAAG,GAAG,IAAI;AAC9D,MAAI,IAAI,OAAO,IAAK,SAAS;AAC7B,MAAI,IAAI,OAAO,IAAI;AACnB,MAAI,IAAI,QAAQ,KAAM,UAAU,KAAK,IAAI,QAAQ,IAAK,IAAI;AAE1D,UAAQ,KAAK,IAAI,KAAK;AAEtB,MAAI,MAAM,KAAK,KAAK,UAAU,UAAU;AACtC,QAAI,MAAM,KAAK,IAAI,IAAI;AACvB,QAAI;AAAA,EACN,OAAO;AACL,QAAI,KAAK,MAAM,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG;AACzC,QAAI,SAAS,IAAI,KAAK,IAAI,GAAG,CAAC,CAAC,KAAK,GAAG;AACrC;AACA,WAAK;AAAA,IACP;AACA,QAAI,IAAI,SAAS,GAAG;AAClB,eAAS,KAAK;AAAA,IAChB,OAAO;AACL,eAAS,KAAK,KAAK,IAAI,GAAG,IAAI,KAAK;AAAA,IACrC;AACA,QAAI,QAAQ,KAAK,GAAG;AAClB;AACA,WAAK;AAAA,IACP;AAEA,QAAI,IAAI,SAAS,MAAM;AACrB,UAAI;AACJ,UAAI;AAAA,IACN,WAAW,IAAI,SAAS,GAAG;AACzB,WAAK,QAAQ,IAAI,KAAK,KAAK,IAAI,GAAG,IAAI;AACtC,UAAI,IAAI;AAAA,IACV,OAAO;AACL,UAAI,QAAQ,KAAK,IAAI,GAAG,QAAQ,CAAC,IAAI,KAAK,IAAI,GAAG,IAAI;AACrD,UAAI;AAAA,IACN;AAAA,EACF;AAEA,SAAO,QAAQ,GAAG,OAAO,SAAS,CAAC,IAAI,IAAI,KAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;AAAA,EAAC;AAE/E,MAAK,KAAK,OAAQ;AAClB,UAAQ;AACR,SAAO,OAAO,GAAG,OAAO,SAAS,CAAC,IAAI,IAAI,KAAM,KAAK,GAAG,KAAK,KAAK,QAAQ,GAAG;AAAA,EAAC;AAE9E,SAAO,SAAS,IAAI,CAAC,KAAK,IAAI;AAChC;AAEA,IAAI,WAAW,CAAC,EAAE;AAElB,IAAI,UAAU,MAAM,WAAW,SAAU,KAAK;AAC5C,SAAO,SAAS,KAAK,GAAG,KAAK;AAC/B;AAEA,IAAI,oBAAoB;AA0BxB,OAAO,sBAAsB,SAAS,wBAAwB,SAC1D,SAAS,sBACT;AAKJ,WAAW;AAEX,SAAS,aAAc;AACrB,SAAO,OAAO,sBACV,aACA;AACN;AAEA,SAAS,aAAc,MAAM,QAAQ;AACnC,MAAI,WAAW,IAAI,QAAQ;AACzB,UAAM,IAAI,WAAW,4BAA4B;AAAA,EACnD;AACA,MAAI,OAAO,qBAAqB;AAE9B,WAAO,IAAI,WAAW,MAAM;AAC5B,SAAK,YAAY,OAAO;AAAA,EAC1B,OAAO;AAEL,QAAI,SAAS,MAAM;AACjB,aAAO,IAAI,OAAO,MAAM;AAAA,IAC1B;AACA,SAAK,SAAS;AAAA,EAChB;AAEA,SAAO;AACT;AAYA,SAAS,OAAQ,KAAK,kBAAkB,QAAQ;AAC9C,MAAI,CAAC,OAAO,uBAAuB,EAAE,gBAAgB,SAAS;AAC5D,WAAO,IAAI,OAAO,KAAK,kBAAkB,MAAM;AAAA,EACjD;AAGA,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,OAAO,qBAAqB,UAAU;AACxC,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,WAAO,YAAY,MAAM,GAAG;AAAA,EAC9B;AACA,SAAO,KAAK,MAAM,KAAK,kBAAkB,MAAM;AACjD;AAEA,OAAO,WAAW;AAGlB,OAAO,WAAW,SAAU,KAAK;AAC/B,MAAI,YAAY,OAAO;AACvB,SAAO;AACT;AAEA,SAAS,KAAM,MAAM,OAAO,kBAAkB,QAAQ;AACpD,MAAI,OAAO,UAAU,UAAU;AAC7B,UAAM,IAAI,UAAU,uCAAuC;AAAA,EAC7D;AAEA,MAAI,OAAO,gBAAgB,eAAe,iBAAiB,aAAa;AACtE,WAAO,gBAAgB,MAAM,OAAO,kBAAkB,MAAM;AAAA,EAC9D;AAEA,MAAI,OAAO,UAAU,UAAU;AAC7B,WAAO,WAAW,MAAM,OAAO,gBAAgB;AAAA,EACjD;AAEA,SAAO,WAAW,MAAM,KAAK;AAC/B;AAUA,OAAO,OAAO,SAAU,OAAO,kBAAkB,QAAQ;AACvD,SAAO,KAAK,MAAM,OAAO,kBAAkB,MAAM;AACnD;AAEA,IAAI,OAAO,qBAAqB;AAC9B,SAAO,UAAU,YAAY,WAAW;AACxC,SAAO,YAAY;AACnB,MAAI,OAAO,WAAW,eAAe,OAAO,WACxC,OAAO,OAAO,OAAO,MAAM,OAAQ;AACzC;AAEA,SAAS,WAAY,MAAM;AACzB,MAAI,OAAO,SAAS,UAAU;AAC5B,UAAM,IAAI,UAAU,kCAAkC;AAAA,EACxD,WAAW,OAAO,GAAG;AACnB,UAAM,IAAI,WAAW,sCAAsC;AAAA,EAC7D;AACF;AAEA,SAAS,MAAO,MAAM,MAAMA,OAAM,UAAU;AAC1C,aAAW,IAAI;AACf,MAAI,QAAQ,GAAG;AACb,WAAO,aAAa,MAAM,IAAI;AAAA,EAChC;AACA,MAAIA,UAAS,QAAW;AAItB,WAAO,OAAO,aAAa,WACvB,aAAa,MAAM,IAAI,EAAE,KAAKA,OAAM,QAAQ,IAC5C,aAAa,MAAM,IAAI,EAAE,KAAKA,KAAI;AAAA,EACxC;AACA,SAAO,aAAa,MAAM,IAAI;AAChC;AAMA,OAAO,QAAQ,SAAU,MAAMA,OAAM,UAAU;AAC7C,SAAO,MAAM,MAAM,MAAMA,OAAM,QAAQ;AACzC;AAEA,SAAS,YAAa,MAAM,MAAM;AAChC,aAAW,IAAI;AACf,SAAO,aAAa,MAAM,OAAO,IAAI,IAAI,QAAQ,IAAI,IAAI,CAAC;AAC1D,MAAI,CAAC,OAAO,qBAAqB;AAC/B,aAAS,IAAI,GAAG,IAAI,MAAM,EAAE,GAAG;AAC7B,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF;AACA,SAAO;AACT;AAKA,OAAO,cAAc,SAAU,MAAM;AACnC,SAAO,YAAY,MAAM,IAAI;AAC/B;AAIA,OAAO,kBAAkB,SAAU,MAAM;AACvC,SAAO,YAAY,MAAM,IAAI;AAC/B;AAEA,SAAS,WAAY,MAAM,QAAQ,UAAU;AAC3C,MAAI,OAAO,aAAa,YAAY,aAAa,IAAI;AACnD,eAAW;AAAA,EACb;AAEA,MAAI,CAAC,OAAO,WAAW,QAAQ,GAAG;AAChC,UAAM,IAAI,UAAU,4CAA4C;AAAA,EAClE;AAEA,MAAI,SAAS,WAAW,QAAQ,QAAQ,IAAI;AAC5C,SAAO,aAAa,MAAM,MAAM;AAEhC,MAAI,SAAS,KAAK,MAAM,QAAQ,QAAQ;AAExC,MAAI,WAAW,QAAQ;AAIrB,WAAO,KAAK,MAAM,GAAG,MAAM;AAAA,EAC7B;AAEA,SAAO;AACT;AAEA,SAAS,cAAe,MAAM,OAAO;AACnC,MAAI,SAAS,MAAM,SAAS,IAAI,IAAI,QAAQ,MAAM,MAAM,IAAI;AAC5D,SAAO,aAAa,MAAM,MAAM;AAChC,WAAS,IAAI,GAAG,IAAI,QAAQ,KAAK,GAAG;AAClC,SAAK,CAAC,IAAI,MAAM,CAAC,IAAI;AAAA,EACvB;AACA,SAAO;AACT;AAEA,SAAS,gBAAiB,MAAM,OAAO,YAAY,QAAQ;AACzD,QAAM;AAEN,MAAI,aAAa,KAAK,MAAM,aAAa,YAAY;AACnD,UAAM,IAAI,WAAW,2BAA6B;AAAA,EACpD;AAEA,MAAI,MAAM,aAAa,cAAc,UAAU,IAAI;AACjD,UAAM,IAAI,WAAW,2BAA6B;AAAA,EACpD;AAEA,MAAI,eAAe,UAAa,WAAW,QAAW;AACpD,YAAQ,IAAI,WAAW,KAAK;AAAA,EAC9B,WAAW,WAAW,QAAW;AAC/B,YAAQ,IAAI,WAAW,OAAO,UAAU;AAAA,EAC1C,OAAO;AACL,YAAQ,IAAI,WAAW,OAAO,YAAY,MAAM;AAAA,EAClD;AAEA,MAAI,OAAO,qBAAqB;AAE9B,WAAO;AACP,SAAK,YAAY,OAAO;AAAA,EAC1B,OAAO;AAEL,WAAO,cAAc,MAAM,KAAK;AAAA,EAClC;AACA,SAAO;AACT;AAEA,SAAS,WAAY,MAAM,KAAK;AAC9B,MAAI,iBAAiB,GAAG,GAAG;AACzB,QAAI,MAAM,QAAQ,IAAI,MAAM,IAAI;AAChC,WAAO,aAAa,MAAM,GAAG;AAE7B,QAAI,KAAK,WAAW,GAAG;AACrB,aAAO;AAAA,IACT;AAEA,QAAI,KAAK,MAAM,GAAG,GAAG,GAAG;AACxB,WAAO;AAAA,EACT;AAEA,MAAI,KAAK;AACP,QAAK,OAAO,gBAAgB,eACxB,IAAI,kBAAkB,eAAgB,YAAY,KAAK;AACzD,UAAI,OAAO,IAAI,WAAW,YAAY,MAAM,IAAI,MAAM,GAAG;AACvD,eAAO,aAAa,MAAM,CAAC;AAAA,MAC7B;AACA,aAAO,cAAc,MAAM,GAAG;AAAA,IAChC;AAEA,QAAI,IAAI,SAAS,YAAY,QAAQ,IAAI,IAAI,GAAG;AAC9C,aAAO,cAAc,MAAM,IAAI,IAAI;AAAA,IACrC;AAAA,EACF;AAEA,QAAM,IAAI,UAAU,oFAAoF;AAC1G;AAEA,SAAS,QAAS,QAAQ;AAGxB,MAAI,UAAU,WAAW,GAAG;AAC1B,UAAM,IAAI,WAAW,4DACa,WAAW,EAAE,SAAS,EAAE,IAAI,QAAQ;AAAA,EACxE;AACA,SAAO,SAAS;AAClB;AACA,OAAO,WAAW;AAClB,SAAS,iBAAkB,GAAG;AAC5B,SAAO,CAAC,EAAE,KAAK,QAAQ,EAAE;AAC3B;AAEA,OAAO,UAAU,SAAS,QAAS,GAAG,GAAG;AACvC,MAAI,CAAC,iBAAiB,CAAC,KAAK,CAAC,iBAAiB,CAAC,GAAG;AAChD,UAAM,IAAI,UAAU,2BAA2B;AAAA,EACjD;AAEA,MAAI,MAAM,EAAG,QAAO;AAEpB,MAAI,IAAI,EAAE;AACV,MAAI,IAAI,EAAE;AAEV,WAAS,IAAI,GAAG,MAAM,KAAK,IAAI,GAAG,CAAC,GAAG,IAAI,KAAK,EAAE,GAAG;AAClD,QAAI,EAAE,CAAC,MAAM,EAAE,CAAC,GAAG;AACjB,UAAI,EAAE,CAAC;AACP,UAAI,EAAE,CAAC;AACP;AAAA,IACF;AAAA,EACF;AAEA,MAAI,IAAI,EAAG,QAAO;AAClB,MAAI,IAAI,EAAG,QAAO;AAClB,SAAO;AACT;AAEA,OAAO,aAAa,SAAS,WAAY,UAAU;AACjD,UAAQ,OAAO,QAAQ,EAAE,YAAY,GAAG;AAAA,IACtC,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AAAA,IACL,KAAK;AACH,aAAO;AAAA,IACT;AACE,aAAO;AAAA,EACX;AACF;AAEA,OAAO,SAAS,SAAS,OAAQ,MAAM,QAAQ;AAC7C,MAAI,CAAC,QAAQ,IAAI,GAAG;AAClB,UAAM,IAAI,UAAU,6CAA6C;AAAA,EACnE;AAEA,MAAI,KAAK,WAAW,GAAG;AACrB,WAAO,OAAO,MAAM,CAAC;AAAA,EACvB;AAEA,MAAI;AACJ,MAAI,WAAW,QAAW;AACxB,aAAS;AACT,SAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,gBAAU,KAAK,CAAC,EAAE;AAAA,IACpB;AAAA,EACF;AAEA,MAAI,SAAS,OAAO,YAAY,MAAM;AACtC,MAAI,MAAM;AACV,OAAK,IAAI,GAAG,IAAI,KAAK,QAAQ,EAAE,GAAG;AAChC,QAAI,MAAM,KAAK,CAAC;AAChB,QAAI,CAAC,iBAAiB,GAAG,GAAG;AAC1B,YAAM,IAAI,UAAU,6CAA6C;AAAA,IACnE;AACA,QAAI,KAAK,QAAQ,GAAG;AACpB,WAAO,IAAI;AAAA,EACb;AACA,SAAO;AACT;AAEA,SAAS,WAAY,QAAQ,UAAU;AACrC,MAAI,iBAAiB,MAAM,GAAG;AAC5B,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,OAAO,gBAAgB,eAAe,OAAO,YAAY,WAAW,eACnE,YAAY,OAAO,MAAM,KAAK,kBAAkB,cAAc;AACjE,WAAO,OAAO;AAAA,EAChB;AACA,MAAI,OAAO,WAAW,UAAU;AAC9B,aAAS,KAAK;AAAA,EAChB;AAEA,MAAI,MAAM,OAAO;AACjB,MAAI,QAAQ,EAAG,QAAO;AAGtB,MAAI,cAAc;AAClB,aAAS;AACP,YAAQ,UAAU;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO;AAAA,MACT,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,YAAY,MAAM,EAAE;AAAA,MAC7B,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,MAAM;AAAA,MACf,KAAK;AACH,eAAO,QAAQ;AAAA,MACjB,KAAK;AACH,eAAO,cAAc,MAAM,EAAE;AAAA,MAC/B;AACE,YAAI,YAAa,QAAO,YAAY,MAAM,EAAE;AAC5C,oBAAY,KAAK,UAAU,YAAY;AACvC,sBAAc;AAAA,IAClB;AAAA,EACF;AACF;AACA,OAAO,aAAa;AAEpB,SAAS,aAAc,UAAU,OAAO,KAAK;AAC3C,MAAI,cAAc;AASlB,MAAI,UAAU,UAAa,QAAQ,GAAG;AACpC,YAAQ;AAAA,EACV;AAGA,MAAI,QAAQ,KAAK,QAAQ;AACvB,WAAO;AAAA,EACT;AAEA,MAAI,QAAQ,UAAa,MAAM,KAAK,QAAQ;AAC1C,UAAM,KAAK;AAAA,EACb;AAEA,MAAI,OAAO,GAAG;AACZ,WAAO;AAAA,EACT;AAGA,WAAS;AACT,aAAW;AAEX,MAAI,OAAO,OAAO;AAChB,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,SAAU,YAAW;AAE1B,SAAO,MAAM;AACX,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,eAAO,SAAS,MAAM,OAAO,GAAG;AAAA,MAElC,KAAK;AAAA,MACL,KAAK;AACH,eAAO,UAAU,MAAM,OAAO,GAAG;AAAA,MAEnC,KAAK;AACH,eAAO,WAAW,MAAM,OAAO,GAAG;AAAA,MAEpC,KAAK;AAAA,MACL,KAAK;AACH,eAAO,YAAY,MAAM,OAAO,GAAG;AAAA,MAErC,KAAK;AACH,eAAO,YAAY,MAAM,OAAO,GAAG;AAAA,MAErC,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,aAAa,MAAM,OAAO,GAAG;AAAA,MAEtC;AACE,YAAI,YAAa,OAAM,IAAI,UAAU,uBAAuB,QAAQ;AACpE,oBAAY,WAAW,IAAI,YAAY;AACvC,sBAAc;AAAA,IAClB;AAAA,EACF;AACF;AAIA,OAAO,UAAU,YAAY;AAE7B,SAAS,KAAM,GAAG,GAAG,GAAG;AACtB,MAAI,IAAI,EAAE,CAAC;AACX,IAAE,CAAC,IAAI,EAAE,CAAC;AACV,IAAE,CAAC,IAAI;AACT;AAEA,OAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,MAAM,GAAG;AACjB,UAAM,IAAI,WAAW,2CAA2C;AAAA,EAClE;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,SAAK,MAAM,GAAG,IAAI,CAAC;AAAA,EACrB;AACA,SAAO;AACT;AAEA,OAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,MAAM,GAAG;AACjB,UAAM,IAAI,WAAW,2CAA2C;AAAA,EAClE;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,SAAK,MAAM,GAAG,IAAI,CAAC;AACnB,SAAK,MAAM,IAAI,GAAG,IAAI,CAAC;AAAA,EACzB;AACA,SAAO;AACT;AAEA,OAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,MAAI,MAAM,KAAK;AACf,MAAI,MAAM,MAAM,GAAG;AACjB,UAAM,IAAI,WAAW,2CAA2C;AAAA,EAClE;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK,GAAG;AAC/B,SAAK,MAAM,GAAG,IAAI,CAAC;AACnB,SAAK,MAAM,IAAI,GAAG,IAAI,CAAC;AACvB,SAAK,MAAM,IAAI,GAAG,IAAI,CAAC;AACvB,SAAK,MAAM,IAAI,GAAG,IAAI,CAAC;AAAA,EACzB;AACA,SAAO;AACT;AAEA,OAAO,UAAU,WAAW,SAASC,YAAY;AAC/C,MAAI,SAAS,KAAK,SAAS;AAC3B,MAAI,WAAW,EAAG,QAAO;AACzB,MAAI,UAAU,WAAW,EAAG,QAAO,UAAU,MAAM,GAAG,MAAM;AAC5D,SAAO,aAAa,MAAM,MAAM,SAAS;AAC3C;AAEA,OAAO,UAAU,SAAS,SAAS,OAAQ,GAAG;AAC5C,MAAI,CAAC,iBAAiB,CAAC,EAAG,OAAM,IAAI,UAAU,2BAA2B;AACzE,MAAI,SAAS,EAAG,QAAO;AACvB,SAAO,OAAO,QAAQ,MAAM,CAAC,MAAM;AACrC;AAEA,OAAO,UAAU,UAAU,SAAS,UAAW;AAC7C,MAAI,MAAM;AACV,MAAI,MAAM;AACV,MAAI,KAAK,SAAS,GAAG;AACnB,UAAM,KAAK,SAAS,OAAO,GAAG,GAAG,EAAE,MAAM,OAAO,EAAE,KAAK,GAAG;AAC1D,QAAI,KAAK,SAAS,IAAK,QAAO;AAAA,EAChC;AACA,SAAO,aAAa,MAAM;AAC5B;AAEA,OAAO,UAAU,UAAU,SAASC,SAAS,QAAQ,OAAO,KAAK,WAAW,SAAS;AACnF,MAAI,CAAC,iBAAiB,MAAM,GAAG;AAC7B,UAAM,IAAI,UAAU,2BAA2B;AAAA,EACjD;AAEA,MAAI,UAAU,QAAW;AACvB,YAAQ;AAAA,EACV;AACA,MAAI,QAAQ,QAAW;AACrB,UAAM,SAAS,OAAO,SAAS;AAAA,EACjC;AACA,MAAI,cAAc,QAAW;AAC3B,gBAAY;AAAA,EACd;AACA,MAAI,YAAY,QAAW;AACzB,cAAU,KAAK;AAAA,EACjB;AAEA,MAAI,QAAQ,KAAK,MAAM,OAAO,UAAU,YAAY,KAAK,UAAU,KAAK,QAAQ;AAC9E,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,MAAI,aAAa,WAAW,SAAS,KAAK;AACxC,WAAO;AAAA,EACT;AACA,MAAI,aAAa,SAAS;AACxB,WAAO;AAAA,EACT;AACA,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,EACT;AAEA,aAAW;AACX,WAAS;AACT,iBAAe;AACf,eAAa;AAEb,MAAI,SAAS,OAAQ,QAAO;AAE5B,MAAI,IAAI,UAAU;AAClB,MAAI,IAAI,MAAM;AACd,MAAI,MAAM,KAAK,IAAI,GAAG,CAAC;AAEvB,MAAI,WAAW,KAAK,MAAM,WAAW,OAAO;AAC5C,MAAI,aAAa,OAAO,MAAM,OAAO,GAAG;AAExC,WAAS,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AAC5B,QAAI,SAAS,CAAC,MAAM,WAAW,CAAC,GAAG;AACjC,UAAI,SAAS,CAAC;AACd,UAAI,WAAW,CAAC;AAChB;AAAA,IACF;AAAA,EACF;AAEA,MAAI,IAAI,EAAG,QAAO;AAClB,MAAI,IAAI,EAAG,QAAO;AAClB,SAAO;AACT;AAWA,SAAS,qBAAsB,QAAQ,KAAK,YAAY,UAAU,KAAK;AAErE,MAAI,OAAO,WAAW,EAAG,QAAO;AAGhC,MAAI,OAAO,eAAe,UAAU;AAClC,eAAW;AACX,iBAAa;AAAA,EACf,WAAW,aAAa,YAAY;AAClC,iBAAa;AAAA,EACf,WAAW,aAAa,aAAa;AACnC,iBAAa;AAAA,EACf;AACA,eAAa,CAAC;AACd,MAAI,MAAM,UAAU,GAAG;AAErB,iBAAa,MAAM,IAAK,OAAO,SAAS;AAAA,EAC1C;AAGA,MAAI,aAAa,EAAG,cAAa,OAAO,SAAS;AACjD,MAAI,cAAc,OAAO,QAAQ;AAC/B,QAAI,IAAK,QAAO;AAAA,QACX,cAAa,OAAO,SAAS;AAAA,EACpC,WAAW,aAAa,GAAG;AACzB,QAAI,IAAK,cAAa;AAAA,QACjB,QAAO;AAAA,EACd;AAGA,MAAI,OAAO,QAAQ,UAAU;AAC3B,UAAM,OAAO,KAAK,KAAK,QAAQ;AAAA,EACjC;AAGA,MAAI,iBAAiB,GAAG,GAAG;AAEzB,QAAI,IAAI,WAAW,GAAG;AACpB,aAAO;AAAA,IACT;AACA,WAAO,aAAa,QAAQ,KAAK,YAAY,UAAU,GAAG;AAAA,EAC5D,WAAW,OAAO,QAAQ,UAAU;AAClC,UAAM,MAAM;AACZ,QAAI,OAAO,uBACP,OAAO,WAAW,UAAU,YAAY,YAAY;AACtD,UAAI,KAAK;AACP,eAAO,WAAW,UAAU,QAAQ,KAAK,QAAQ,KAAK,UAAU;AAAA,MAClE,OAAO;AACL,eAAO,WAAW,UAAU,YAAY,KAAK,QAAQ,KAAK,UAAU;AAAA,MACtE;AAAA,IACF;AACA,WAAO,aAAa,QAAQ,CAAE,GAAI,GAAG,YAAY,UAAU,GAAG;AAAA,EAChE;AAEA,QAAM,IAAI,UAAU,sCAAsC;AAC5D;AAEA,SAAS,aAAc,KAAK,KAAK,YAAY,UAAU,KAAK;AAC1D,MAAI,YAAY;AAChB,MAAI,YAAY,IAAI;AACpB,MAAI,YAAY,IAAI;AAEpB,MAAI,aAAa,QAAW;AAC1B,eAAW,OAAO,QAAQ,EAAE,YAAY;AACxC,QAAI,aAAa,UAAU,aAAa,WACpC,aAAa,aAAa,aAAa,YAAY;AACrD,UAAI,IAAI,SAAS,KAAK,IAAI,SAAS,GAAG;AACpC,eAAO;AAAA,MACT;AACA,kBAAY;AACZ,mBAAa;AACb,mBAAa;AACb,oBAAc;AAAA,IAChB;AAAA,EACF;AAEA,WAASC,MAAM,KAAKC,IAAG;AACrB,QAAI,cAAc,GAAG;AACnB,aAAO,IAAIA,EAAC;AAAA,IACd,OAAO;AACL,aAAO,IAAI,aAAaA,KAAI,SAAS;AAAA,IACvC;AAAA,EACF;AAEA,MAAI;AACJ,MAAI,KAAK;AACP,QAAI,aAAa;AACjB,SAAK,IAAI,YAAY,IAAI,WAAW,KAAK;AACvC,UAAID,MAAK,KAAK,CAAC,MAAMA,MAAK,KAAK,eAAe,KAAK,IAAI,IAAI,UAAU,GAAG;AACtE,YAAI,eAAe,GAAI,cAAa;AACpC,YAAI,IAAI,aAAa,MAAM,UAAW,QAAO,aAAa;AAAA,MAC5D,OAAO;AACL,YAAI,eAAe,GAAI,MAAK,IAAI;AAChC,qBAAa;AAAA,MACf;AAAA,IACF;AAAA,EACF,OAAO;AACL,QAAI,aAAa,YAAY,UAAW,cAAa,YAAY;AACjE,SAAK,IAAI,YAAY,KAAK,GAAG,KAAK;AAChC,UAAI,QAAQ;AACZ,eAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,YAAIA,MAAK,KAAK,IAAI,CAAC,MAAMA,MAAK,KAAK,CAAC,GAAG;AACrC,kBAAQ;AACR;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAO,QAAO;AAAA,IACpB;AAAA,EACF;AAEA,SAAO;AACT;AAEA,OAAO,UAAU,WAAW,SAAS,SAAU,KAAK,YAAY,UAAU;AACxE,SAAO,KAAK,QAAQ,KAAK,YAAY,QAAQ,MAAM;AACrD;AAEA,OAAO,UAAU,UAAU,SAAS,QAAS,KAAK,YAAY,UAAU;AACtE,SAAO,qBAAqB,MAAM,KAAK,YAAY,UAAU,IAAI;AACnE;AAEA,OAAO,UAAU,cAAc,SAAS,YAAa,KAAK,YAAY,UAAU;AAC9E,SAAO,qBAAqB,MAAM,KAAK,YAAY,UAAU,KAAK;AACpE;AAEA,SAAS,SAAU,KAAK,QAAQ,QAAQ,QAAQ;AAC9C,WAAS,OAAO,MAAM,KAAK;AAC3B,MAAI,YAAY,IAAI,SAAS;AAC7B,MAAI,CAAC,QAAQ;AACX,aAAS;AAAA,EACX,OAAO;AACL,aAAS,OAAO,MAAM;AACtB,QAAI,SAAS,WAAW;AACtB,eAAS;AAAA,IACX;AAAA,EACF;AAGA,MAAI,SAAS,OAAO;AACpB,MAAI,SAAS,MAAM,EAAG,OAAM,IAAI,UAAU,oBAAoB;AAE9D,MAAI,SAAS,SAAS,GAAG;AACvB,aAAS,SAAS;AAAA,EACpB;AACA,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,QAAI,SAAS,SAAS,OAAO,OAAO,IAAI,GAAG,CAAC,GAAG,EAAE;AACjD,QAAI,MAAM,MAAM,EAAG,QAAO;AAC1B,QAAI,SAAS,CAAC,IAAI;AAAA,EACpB;AACA,SAAO;AACT;AAEA,SAAS,UAAW,KAAK,QAAQ,QAAQ,QAAQ;AAC/C,SAAO,WAAW,YAAY,QAAQ,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,MAAM;AACjF;AAEA,SAAS,WAAY,KAAK,QAAQ,QAAQ,QAAQ;AAChD,SAAO,WAAW,aAAa,MAAM,GAAG,KAAK,QAAQ,MAAM;AAC7D;AAEA,SAAS,YAAa,KAAK,QAAQ,QAAQ,QAAQ;AACjD,SAAO,WAAW,KAAK,QAAQ,QAAQ,MAAM;AAC/C;AAEA,SAAS,YAAa,KAAK,QAAQ,QAAQ,QAAQ;AACjD,SAAO,WAAW,cAAc,MAAM,GAAG,KAAK,QAAQ,MAAM;AAC9D;AAEA,SAAS,UAAW,KAAK,QAAQ,QAAQ,QAAQ;AAC/C,SAAO,WAAW,eAAe,QAAQ,IAAI,SAAS,MAAM,GAAG,KAAK,QAAQ,MAAM;AACpF;AAEA,OAAO,UAAU,QAAQ,SAASE,OAAO,QAAQ,QAAQ,QAAQ,UAAU;AAEzE,MAAI,WAAW,QAAW;AACxB,eAAW;AACX,aAAS,KAAK;AACd,aAAS;AAAA,EAEX,WAAW,WAAW,UAAa,OAAO,WAAW,UAAU;AAC7D,eAAW;AACX,aAAS,KAAK;AACd,aAAS;AAAA,EAEX,WAAW,SAAS,MAAM,GAAG;AAC3B,aAAS,SAAS;AAClB,QAAI,SAAS,MAAM,GAAG;AACpB,eAAS,SAAS;AAClB,UAAI,aAAa,OAAW,YAAW;AAAA,IACzC,OAAO;AACL,iBAAW;AACX,eAAS;AAAA,IACX;AAAA,EAEF,OAAO;AACL,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAEA,MAAI,YAAY,KAAK,SAAS;AAC9B,MAAI,WAAW,UAAa,SAAS,UAAW,UAAS;AAEzD,MAAK,OAAO,SAAS,MAAM,SAAS,KAAK,SAAS,MAAO,SAAS,KAAK,QAAQ;AAC7E,UAAM,IAAI,WAAW,wCAAwC;AAAA,EAC/D;AAEA,MAAI,CAAC,SAAU,YAAW;AAE1B,MAAI,cAAc;AAClB,aAAS;AACP,YAAQ,UAAU;AAAA,MAChB,KAAK;AACH,eAAO,SAAS,MAAM,QAAQ,QAAQ,MAAM;AAAA,MAE9C,KAAK;AAAA,MACL,KAAK;AACH,eAAO,UAAU,MAAM,QAAQ,QAAQ,MAAM;AAAA,MAE/C,KAAK;AACH,eAAO,WAAW,MAAM,QAAQ,QAAQ,MAAM;AAAA,MAEhD,KAAK;AAAA,MACL,KAAK;AACH,eAAO,YAAY,MAAM,QAAQ,QAAQ,MAAM;AAAA,MAEjD,KAAK;AAEH,eAAO,YAAY,MAAM,QAAQ,QAAQ,MAAM;AAAA,MAEjD,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACH,eAAO,UAAU,MAAM,QAAQ,QAAQ,MAAM;AAAA,MAE/C;AACE,YAAI,YAAa,OAAM,IAAI,UAAU,uBAAuB,QAAQ;AACpE,oBAAY,KAAK,UAAU,YAAY;AACvC,sBAAc;AAAA,IAClB;AAAA,EACF;AACF;AAEA,OAAO,UAAU,SAAS,SAAS,SAAU;AAC3C,SAAO;AAAA,IACL,MAAM;AAAA,IACN,MAAM,MAAM,UAAU,MAAM,KAAK,KAAK,QAAQ,MAAM,CAAC;AAAA,EACvD;AACF;AAEA,SAAS,YAAa,KAAK,OAAO,KAAK;AACrC,MAAI,UAAU,KAAK,QAAQ,IAAI,QAAQ;AACrC,WAAO,cAAc,GAAG;AAAA,EAC1B,OAAO;AACL,WAAO,cAAc,IAAI,MAAM,OAAO,GAAG,CAAC;AAAA,EAC5C;AACF;AAEA,SAAS,UAAW,KAAK,OAAO,KAAK;AACnC,QAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAC9B,MAAI,MAAM,CAAC;AAEX,MAAI,IAAI;AACR,SAAO,IAAI,KAAK;AACd,QAAI,YAAY,IAAI,CAAC;AACrB,QAAI,YAAY;AAChB,QAAI,mBAAoB,YAAY,MAAQ,IACvC,YAAY,MAAQ,IACpB,YAAY,MAAQ,IACrB;AAEJ,QAAI,IAAI,oBAAoB,KAAK;AAC/B,UAAI,YAAY,WAAW,YAAY;AAEvC,cAAQ,kBAAkB;AAAA,QACxB,KAAK;AACH,cAAI,YAAY,KAAM;AACpB,wBAAY;AAAA,UACd;AACA;AAAA,QACF,KAAK;AACH,uBAAa,IAAI,IAAI,CAAC;AACtB,eAAK,aAAa,SAAU,KAAM;AAChC,6BAAiB,YAAY,OAAS,IAAO,aAAa;AAC1D,gBAAI,gBAAgB,KAAM;AACxB,0BAAY;AAAA,YACd;AAAA,UACF;AACA;AAAA,QACF,KAAK;AACH,uBAAa,IAAI,IAAI,CAAC;AACtB,sBAAY,IAAI,IAAI,CAAC;AACrB,eAAK,aAAa,SAAU,QAAS,YAAY,SAAU,KAAM;AAC/D,6BAAiB,YAAY,OAAQ,MAAO,aAAa,OAAS,IAAO,YAAY;AACrF,gBAAI,gBAAgB,SAAU,gBAAgB,SAAU,gBAAgB,QAAS;AAC/E,0BAAY;AAAA,YACd;AAAA,UACF;AACA;AAAA,QACF,KAAK;AACH,uBAAa,IAAI,IAAI,CAAC;AACtB,sBAAY,IAAI,IAAI,CAAC;AACrB,uBAAa,IAAI,IAAI,CAAC;AACtB,eAAK,aAAa,SAAU,QAAS,YAAY,SAAU,QAAS,aAAa,SAAU,KAAM;AAC/F,6BAAiB,YAAY,OAAQ,MAAQ,aAAa,OAAS,MAAO,YAAY,OAAS,IAAO,aAAa;AACnH,gBAAI,gBAAgB,SAAU,gBAAgB,SAAU;AACtD,0BAAY;AAAA,YACd;AAAA,UACF;AAAA,MACJ;AAAA,IACF;AAEA,QAAI,cAAc,MAAM;AAGtB,kBAAY;AACZ,yBAAmB;AAAA,IACrB,WAAW,YAAY,OAAQ;AAE7B,mBAAa;AACb,UAAI,KAAK,cAAc,KAAK,OAAQ,KAAM;AAC1C,kBAAY,QAAS,YAAY;AAAA,IACnC;AAEA,QAAI,KAAK,SAAS;AAClB,SAAK;AAAA,EACP;AAEA,SAAO,sBAAsB,GAAG;AAClC;AAKA,IAAI,uBAAuB;AAE3B,SAAS,sBAAuB,YAAY;AAC1C,MAAI,MAAM,WAAW;AACrB,MAAI,OAAO,sBAAsB;AAC/B,WAAO,OAAO,aAAa,MAAM,QAAQ,UAAU;AAAA,EACrD;AAGA,MAAI,MAAM;AACV,MAAI,IAAI;AACR,SAAO,IAAI,KAAK;AACd,WAAO,OAAO,aAAa;AAAA,MACzB;AAAA,MACA,WAAW,MAAM,GAAG,KAAK,oBAAoB;AAAA,IAC/C;AAAA,EACF;AACA,SAAO;AACT;AAEA,SAAS,WAAY,KAAK,OAAO,KAAK;AACpC,MAAI,MAAM;AACV,QAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAE9B,WAAS,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAChC,WAAO,OAAO,aAAa,IAAI,CAAC,IAAI,GAAI;AAAA,EAC1C;AACA,SAAO;AACT;AAEA,SAAS,YAAa,KAAK,OAAO,KAAK;AACrC,MAAI,MAAM;AACV,QAAM,KAAK,IAAI,IAAI,QAAQ,GAAG;AAE9B,WAAS,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAChC,WAAO,OAAO,aAAa,IAAI,CAAC,CAAC;AAAA,EACnC;AACA,SAAO;AACT;AAEA,SAAS,SAAU,KAAK,OAAO,KAAK;AAClC,MAAI,MAAM,IAAI;AAEd,MAAI,CAAC,SAAS,QAAQ,EAAG,SAAQ;AACjC,MAAI,CAAC,OAAO,MAAM,KAAK,MAAM,IAAK,OAAM;AAExC,MAAI,MAAM;AACV,WAAS,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAChC,WAAO,MAAM,IAAI,CAAC,CAAC;AAAA,EACrB;AACA,SAAO;AACT;AAEA,SAAS,aAAc,KAAK,OAAO,KAAK;AACtC,MAAI,QAAQ,IAAI,MAAM,OAAO,GAAG;AAChC,MAAI,MAAM;AACV,WAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK,GAAG;AACxC,WAAO,OAAO,aAAa,MAAM,CAAC,IAAI,MAAM,IAAI,CAAC,IAAI,GAAG;AAAA,EAC1D;AACA,SAAO;AACT;AAEA,OAAO,UAAU,QAAQ,SAAS,MAAO,OAAO,KAAK;AACnD,MAAI,MAAM,KAAK;AACf,UAAQ,CAAC,CAAC;AACV,QAAM,QAAQ,SAAY,MAAM,CAAC,CAAC;AAElC,MAAI,QAAQ,GAAG;AACb,aAAS;AACT,QAAI,QAAQ,EAAG,SAAQ;AAAA,EACzB,WAAW,QAAQ,KAAK;AACtB,YAAQ;AAAA,EACV;AAEA,MAAI,MAAM,GAAG;AACX,WAAO;AACP,QAAI,MAAM,EAAG,OAAM;AAAA,EACrB,WAAW,MAAM,KAAK;AACpB,UAAM;AAAA,EACR;AAEA,MAAI,MAAM,MAAO,OAAM;AAEvB,MAAI;AACJ,MAAI,OAAO,qBAAqB;AAC9B,aAAS,KAAK,SAAS,OAAO,GAAG;AACjC,WAAO,YAAY,OAAO;AAAA,EAC5B,OAAO;AACL,QAAI,WAAW,MAAM;AACrB,aAAS,IAAI,OAAO,UAAU,MAAS;AACvC,aAAS,IAAI,GAAG,IAAI,UAAU,EAAE,GAAG;AACjC,aAAO,CAAC,IAAI,KAAK,IAAI,KAAK;AAAA,IAC5B;AAAA,EACF;AAEA,SAAO;AACT;AAKA,SAAS,YAAa,QAAQ,KAAK,QAAQ;AACzC,MAAK,SAAS,MAAO,KAAK,SAAS,EAAG,OAAM,IAAI,WAAW,oBAAoB;AAC/E,MAAI,SAAS,MAAM,OAAQ,OAAM,IAAI,WAAW,uCAAuC;AACzF;AAEA,OAAO,UAAU,aAAa,SAAS,WAAY,QAAQC,aAAY,UAAU;AAC/E,WAAS,SAAS;AAClB,EAAAA,cAAaA,cAAa;AAC1B,MAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,MAAI,MAAM,KAAK,MAAM;AACrB,MAAI,MAAM;AACV,MAAI,IAAI;AACR,SAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,WAAO,KAAK,SAAS,CAAC,IAAI;AAAA,EAC5B;AAEA,SAAO;AACT;AAEA,OAAO,UAAU,aAAa,SAAS,WAAY,QAAQA,aAAY,UAAU;AAC/E,WAAS,SAAS;AAClB,EAAAA,cAAaA,cAAa;AAC1B,MAAI,CAAC,UAAU;AACb,gBAAY,QAAQA,aAAY,KAAK,MAAM;AAAA,EAC7C;AAEA,MAAI,MAAM,KAAK,SAAS,EAAEA,WAAU;AACpC,MAAI,MAAM;AACV,SAAOA,cAAa,MAAM,OAAO,MAAQ;AACvC,WAAO,KAAK,SAAS,EAAEA,WAAU,IAAI;AAAA,EACvC;AAEA,SAAO;AACT;AAEA,OAAO,UAAU,YAAY,SAAS,UAAW,QAAQ,UAAU;AACjE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,SAAO,KAAK,MAAM;AACpB;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,SAAO,KAAK,MAAM,IAAK,KAAK,SAAS,CAAC,KAAK;AAC7C;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,SAAQ,KAAK,MAAM,KAAK,IAAK,KAAK,SAAS,CAAC;AAC9C;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,UAAS,KAAK,MAAM,IACf,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC,KAAK,MACpB,KAAK,SAAS,CAAC,IAAI;AAC1B;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,SAAQ,KAAK,MAAM,IAAI,YACnB,KAAK,SAAS,CAAC,KAAK,KACrB,KAAK,SAAS,CAAC,KAAK,IACrB,KAAK,SAAS,CAAC;AACnB;AAEA,OAAO,UAAU,YAAY,SAAS,UAAW,QAAQA,aAAY,UAAU;AAC7E,WAAS,SAAS;AAClB,EAAAA,cAAaA,cAAa;AAC1B,MAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,MAAI,MAAM,KAAK,MAAM;AACrB,MAAI,MAAM;AACV,MAAI,IAAI;AACR,SAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,WAAO,KAAK,SAAS,CAAC,IAAI;AAAA,EAC5B;AACA,SAAO;AAEP,MAAI,OAAO,IAAK,QAAO,KAAK,IAAI,GAAG,IAAIA,WAAU;AAEjD,SAAO;AACT;AAEA,OAAO,UAAU,YAAY,SAAS,UAAW,QAAQA,aAAY,UAAU;AAC7E,WAAS,SAAS;AAClB,EAAAA,cAAaA,cAAa;AAC1B,MAAI,CAAC,SAAU,aAAY,QAAQA,aAAY,KAAK,MAAM;AAE1D,MAAI,IAAIA;AACR,MAAI,MAAM;AACV,MAAI,MAAM,KAAK,SAAS,EAAE,CAAC;AAC3B,SAAO,IAAI,MAAM,OAAO,MAAQ;AAC9B,WAAO,KAAK,SAAS,EAAE,CAAC,IAAI;AAAA,EAC9B;AACA,SAAO;AAEP,MAAI,OAAO,IAAK,QAAO,KAAK,IAAI,GAAG,IAAIA,WAAU;AAEjD,SAAO;AACT;AAEA,OAAO,UAAU,WAAW,SAAS,SAAU,QAAQ,UAAU;AAC/D,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,MAAI,EAAE,KAAK,MAAM,IAAI,KAAO,QAAQ,KAAK,MAAM;AAC/C,UAAS,MAAO,KAAK,MAAM,IAAI,KAAK;AACtC;AAEA,OAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,MAAI,MAAM,KAAK,MAAM,IAAK,KAAK,SAAS,CAAC,KAAK;AAC9C,SAAQ,MAAM,QAAU,MAAM,aAAa;AAC7C;AAEA,OAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,MAAI,MAAM,KAAK,SAAS,CAAC,IAAK,KAAK,MAAM,KAAK;AAC9C,SAAQ,MAAM,QAAU,MAAM,aAAa;AAC7C;AAEA,OAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,SAAQ,KAAK,MAAM,IAChB,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC,KAAK,KACpB,KAAK,SAAS,CAAC,KAAK;AACzB;AAEA,OAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AAEjD,SAAQ,KAAK,MAAM,KAAK,KACrB,KAAK,SAAS,CAAC,KAAK,KACpB,KAAK,SAAS,CAAC,KAAK,IACpB,KAAK,SAAS,CAAC;AACpB;AAEA,OAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,SAAO,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC;AACvC;AAEA,OAAO,UAAU,cAAc,SAAS,YAAa,QAAQ,UAAU;AACrE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,SAAO,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC;AACxC;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,SAAO,KAAK,MAAM,QAAQ,MAAM,IAAI,CAAC;AACvC;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,QAAQ,UAAU;AACvE,MAAI,CAAC,SAAU,aAAY,QAAQ,GAAG,KAAK,MAAM;AACjD,SAAO,KAAK,MAAM,QAAQ,OAAO,IAAI,CAAC;AACxC;AAEA,SAAS,SAAU,KAAK,OAAO,QAAQ,KAAK,KAAK,KAAK;AACpD,MAAI,CAAC,iBAAiB,GAAG,EAAG,OAAM,IAAI,UAAU,6CAA6C;AAC7F,MAAI,QAAQ,OAAO,QAAQ,IAAK,OAAM,IAAI,WAAW,mCAAmC;AACxF,MAAI,SAAS,MAAM,IAAI,OAAQ,OAAM,IAAI,WAAW,oBAAoB;AAC1E;AAEA,OAAO,UAAU,cAAc,SAAS,YAAa,OAAO,QAAQA,aAAY,UAAU;AACxF,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,EAAAA,cAAaA,cAAa;AAC1B,MAAI,CAAC,UAAU;AACb,QAAI,WAAW,KAAK,IAAI,GAAG,IAAIA,WAAU,IAAI;AAC7C,aAAS,MAAM,OAAO,QAAQA,aAAY,UAAU,CAAC;AAAA,EACvD;AAEA,MAAI,MAAM;AACV,MAAI,IAAI;AACR,OAAK,MAAM,IAAI,QAAQ;AACvB,SAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,SAAK,SAAS,CAAC,IAAK,QAAQ,MAAO;AAAA,EACrC;AAEA,SAAO,SAASA;AAClB;AAEA,OAAO,UAAU,cAAc,SAAS,YAAa,OAAO,QAAQA,aAAY,UAAU;AACxF,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,EAAAA,cAAaA,cAAa;AAC1B,MAAI,CAAC,UAAU;AACb,QAAI,WAAW,KAAK,IAAI,GAAG,IAAIA,WAAU,IAAI;AAC7C,aAAS,MAAM,OAAO,QAAQA,aAAY,UAAU,CAAC;AAAA,EACvD;AAEA,MAAI,IAAIA,cAAa;AACrB,MAAI,MAAM;AACV,OAAK,SAAS,CAAC,IAAI,QAAQ;AAC3B,SAAO,EAAE,KAAK,MAAM,OAAO,MAAQ;AACjC,SAAK,SAAS,CAAC,IAAK,QAAQ,MAAO;AAAA,EACrC;AAEA,SAAO,SAASA;AAClB;AAEA,OAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQ,UAAU;AAC1E,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,KAAM,CAAC;AACvD,MAAI,CAAC,OAAO,oBAAqB,SAAQ,KAAK,MAAM,KAAK;AACzD,OAAK,MAAM,IAAK,QAAQ;AACxB,SAAO,SAAS;AAClB;AAEA,SAAS,kBAAmB,KAAK,OAAO,QAAQ,cAAc;AAC5D,MAAI,QAAQ,EAAG,SAAQ,QAAS,QAAQ;AACxC,WAAS,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,SAAS,QAAQ,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AAChE,QAAI,SAAS,CAAC,KAAK,QAAS,OAAS,KAAK,eAAe,IAAI,IAAI,SAC9D,eAAe,IAAI,IAAI,KAAK;AAAA,EACjC;AACF;AAEA,OAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,CAAC;AACzD,MAAI,OAAO,qBAAqB;AAC9B,SAAK,MAAM,IAAK,QAAQ;AACxB,SAAK,SAAS,CAAC,IAAK,UAAU;AAAA,EAChC,OAAO;AACL,sBAAkB,MAAM,OAAO,QAAQ,IAAI;AAAA,EAC7C;AACA,SAAO,SAAS;AAClB;AAEA,OAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,CAAC;AACzD,MAAI,OAAO,qBAAqB;AAC9B,SAAK,MAAM,IAAK,UAAU;AAC1B,SAAK,SAAS,CAAC,IAAK,QAAQ;AAAA,EAC9B,OAAO;AACL,sBAAkB,MAAM,OAAO,QAAQ,KAAK;AAAA,EAC9C;AACA,SAAO,SAAS;AAClB;AAEA,SAAS,kBAAmB,KAAK,OAAO,QAAQ,cAAc;AAC5D,MAAI,QAAQ,EAAG,SAAQ,aAAa,QAAQ;AAC5C,WAAS,IAAI,GAAG,IAAI,KAAK,IAAI,IAAI,SAAS,QAAQ,CAAC,GAAG,IAAI,GAAG,EAAE,GAAG;AAChE,QAAI,SAAS,CAAC,IAAK,WAAW,eAAe,IAAI,IAAI,KAAK,IAAK;AAAA,EACjE;AACF;AAEA,OAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,CAAC;AAC7D,MAAI,OAAO,qBAAqB;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,MAAM,IAAK,QAAQ;AAAA,EAC1B,OAAO;AACL,sBAAkB,MAAM,OAAO,QAAQ,IAAI;AAAA,EAC7C;AACA,SAAO,SAAS;AAClB;AAEA,OAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,CAAC;AAC7D,MAAI,OAAO,qBAAqB;AAC9B,SAAK,MAAM,IAAK,UAAU;AAC1B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,QAAQ;AAAA,EAC9B,OAAO;AACL,sBAAkB,MAAM,OAAO,QAAQ,KAAK;AAAA,EAC9C;AACA,SAAO,SAAS;AAClB;AAEA,OAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQA,aAAY,UAAU;AACtF,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,UAAU;AACb,QAAI,QAAQ,KAAK,IAAI,GAAG,IAAIA,cAAa,CAAC;AAE1C,aAAS,MAAM,OAAO,QAAQA,aAAY,QAAQ,GAAG,CAAC,KAAK;AAAA,EAC7D;AAEA,MAAI,IAAI;AACR,MAAI,MAAM;AACV,MAAI,MAAM;AACV,OAAK,MAAM,IAAI,QAAQ;AACvB,SAAO,EAAE,IAAIA,gBAAe,OAAO,MAAQ;AACzC,QAAI,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG;AACxD,YAAM;AAAA,IACR;AACA,SAAK,SAAS,CAAC,KAAM,QAAQ,OAAQ,KAAK,MAAM;AAAA,EAClD;AAEA,SAAO,SAASA;AAClB;AAEA,OAAO,UAAU,aAAa,SAAS,WAAY,OAAO,QAAQA,aAAY,UAAU;AACtF,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,UAAU;AACb,QAAI,QAAQ,KAAK,IAAI,GAAG,IAAIA,cAAa,CAAC;AAE1C,aAAS,MAAM,OAAO,QAAQA,aAAY,QAAQ,GAAG,CAAC,KAAK;AAAA,EAC7D;AAEA,MAAI,IAAIA,cAAa;AACrB,MAAI,MAAM;AACV,MAAI,MAAM;AACV,OAAK,SAAS,CAAC,IAAI,QAAQ;AAC3B,SAAO,EAAE,KAAK,MAAM,OAAO,MAAQ;AACjC,QAAI,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,IAAI,CAAC,MAAM,GAAG;AACxD,YAAM;AAAA,IACR;AACA,SAAK,SAAS,CAAC,KAAM,QAAQ,OAAQ,KAAK,MAAM;AAAA,EAClD;AAEA,SAAO,SAASA;AAClB;AAEA,OAAO,UAAU,YAAY,SAAS,UAAW,OAAO,QAAQ,UAAU;AACxE,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,KAAM,IAAK;AAC3D,MAAI,CAAC,OAAO,oBAAqB,SAAQ,KAAK,MAAM,KAAK;AACzD,MAAI,QAAQ,EAAG,SAAQ,MAAO,QAAQ;AACtC,OAAK,MAAM,IAAK,QAAQ;AACxB,SAAO,SAAS;AAClB;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,MAAO;AAC/D,MAAI,OAAO,qBAAqB;AAC9B,SAAK,MAAM,IAAK,QAAQ;AACxB,SAAK,SAAS,CAAC,IAAK,UAAU;AAAA,EAChC,OAAO;AACL,sBAAkB,MAAM,OAAO,QAAQ,IAAI;AAAA,EAC7C;AACA,SAAO,SAAS;AAClB;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,OAAQ,MAAO;AAC/D,MAAI,OAAO,qBAAqB;AAC9B,SAAK,MAAM,IAAK,UAAU;AAC1B,SAAK,SAAS,CAAC,IAAK,QAAQ;AAAA,EAC9B,OAAO;AACL,sBAAkB,MAAM,OAAO,QAAQ,KAAK;AAAA,EAC9C;AACA,SAAO,SAAS;AAClB;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,WAAW;AACvE,MAAI,OAAO,qBAAqB;AAC9B,SAAK,MAAM,IAAK,QAAQ;AACxB,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAAA,EAChC,OAAO;AACL,sBAAkB,MAAM,OAAO,QAAQ,IAAI;AAAA,EAC7C;AACA,SAAO,SAAS;AAClB;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,UAAQ,CAAC;AACT,WAAS,SAAS;AAClB,MAAI,CAAC,SAAU,UAAS,MAAM,OAAO,QAAQ,GAAG,YAAY,WAAW;AACvE,MAAI,QAAQ,EAAG,SAAQ,aAAa,QAAQ;AAC5C,MAAI,OAAO,qBAAqB;AAC9B,SAAK,MAAM,IAAK,UAAU;AAC1B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,UAAU;AAC9B,SAAK,SAAS,CAAC,IAAK,QAAQ;AAAA,EAC9B,OAAO;AACL,sBAAkB,MAAM,OAAO,QAAQ,KAAK;AAAA,EAC9C;AACA,SAAO,SAAS;AAClB;AAEA,SAAS,aAAc,KAAK,OAAO,QAAQ,KAAK,KAAK,KAAK;AACxD,MAAI,SAAS,MAAM,IAAI,OAAQ,OAAM,IAAI,WAAW,oBAAoB;AACxE,MAAI,SAAS,EAAG,OAAM,IAAI,WAAW,oBAAoB;AAC3D;AAEA,SAAS,WAAY,KAAK,OAAO,QAAQ,cAAc,UAAU;AAC/D,MAAI,CAAC,UAAU;AACb,iBAAa,KAAK,OAAO,QAAQ,CAAC;AAAA,EACpC;AACA,QAAM,KAAK,OAAO,QAAQ,cAAc,IAAI,CAAC;AAC7C,SAAO,SAAS;AAClB;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,SAAO,WAAW,MAAM,OAAO,QAAQ,MAAM,QAAQ;AACvD;AAEA,OAAO,UAAU,eAAe,SAAS,aAAc,OAAO,QAAQ,UAAU;AAC9E,SAAO,WAAW,MAAM,OAAO,QAAQ,OAAO,QAAQ;AACxD;AAEA,SAAS,YAAa,KAAK,OAAO,QAAQ,cAAc,UAAU;AAChE,MAAI,CAAC,UAAU;AACb,iBAAa,KAAK,OAAO,QAAQ,CAAC;AAAA,EACpC;AACA,QAAM,KAAK,OAAO,QAAQ,cAAc,IAAI,CAAC;AAC7C,SAAO,SAAS;AAClB;AAEA,OAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,SAAO,YAAY,MAAM,OAAO,QAAQ,MAAM,QAAQ;AACxD;AAEA,OAAO,UAAU,gBAAgB,SAAS,cAAe,OAAO,QAAQ,UAAU;AAChF,SAAO,YAAY,MAAM,OAAO,QAAQ,OAAO,QAAQ;AACzD;AAGA,OAAO,UAAU,OAAO,SAAS,KAAM,QAAQ,aAAa,OAAO,KAAK;AACtE,MAAI,CAAC,MAAO,SAAQ;AACpB,MAAI,CAAC,OAAO,QAAQ,EAAG,OAAM,KAAK;AAClC,MAAI,eAAe,OAAO,OAAQ,eAAc,OAAO;AACvD,MAAI,CAAC,YAAa,eAAc;AAChC,MAAI,MAAM,KAAK,MAAM,MAAO,OAAM;AAGlC,MAAI,QAAQ,MAAO,QAAO;AAC1B,MAAI,OAAO,WAAW,KAAK,KAAK,WAAW,EAAG,QAAO;AAGrD,MAAI,cAAc,GAAG;AACnB,UAAM,IAAI,WAAW,2BAA2B;AAAA,EAClD;AACA,MAAI,QAAQ,KAAK,SAAS,KAAK,OAAQ,OAAM,IAAI,WAAW,2BAA2B;AACvF,MAAI,MAAM,EAAG,OAAM,IAAI,WAAW,yBAAyB;AAG3D,MAAI,MAAM,KAAK,OAAQ,OAAM,KAAK;AAClC,MAAI,OAAO,SAAS,cAAc,MAAM,OAAO;AAC7C,UAAM,OAAO,SAAS,cAAc;AAAA,EACtC;AAEA,MAAI,MAAM,MAAM;AAChB,MAAI;AAEJ,MAAI,SAAS,UAAU,QAAQ,eAAe,cAAc,KAAK;AAE/D,SAAK,IAAI,MAAM,GAAG,KAAK,GAAG,EAAE,GAAG;AAC7B,aAAO,IAAI,WAAW,IAAI,KAAK,IAAI,KAAK;AAAA,IAC1C;AAAA,EACF,WAAW,MAAM,OAAQ,CAAC,OAAO,qBAAqB;AAEpD,SAAK,IAAI,GAAG,IAAI,KAAK,EAAE,GAAG;AACxB,aAAO,IAAI,WAAW,IAAI,KAAK,IAAI,KAAK;AAAA,IAC1C;AAAA,EACF,OAAO;AACL,eAAW,UAAU,IAAI;AAAA,MACvB;AAAA,MACA,KAAK,SAAS,OAAO,QAAQ,GAAG;AAAA,MAChC;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AACT;AAMA,OAAO,UAAU,OAAO,SAAS,KAAM,KAAK,OAAO,KAAK,UAAU;AAEhE,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI,OAAO,UAAU,UAAU;AAC7B,iBAAW;AACX,cAAQ;AACR,YAAM,KAAK;AAAA,IACb,WAAW,OAAO,QAAQ,UAAU;AAClC,iBAAW;AACX,YAAM,KAAK;AAAA,IACb;AACA,QAAI,IAAI,WAAW,GAAG;AACpB,UAAI,OAAO,IAAI,WAAW,CAAC;AAC3B,UAAI,OAAO,KAAK;AACd,cAAM;AAAA,MACR;AAAA,IACF;AACA,QAAI,aAAa,UAAa,OAAO,aAAa,UAAU;AAC1D,YAAM,IAAI,UAAU,2BAA2B;AAAA,IACjD;AACA,QAAI,OAAO,aAAa,YAAY,CAAC,OAAO,WAAW,QAAQ,GAAG;AAChE,YAAM,IAAI,UAAU,uBAAuB,QAAQ;AAAA,IACrD;AAAA,EACF,WAAW,OAAO,QAAQ,UAAU;AAClC,UAAM,MAAM;AAAA,EACd;AAGA,MAAI,QAAQ,KAAK,KAAK,SAAS,SAAS,KAAK,SAAS,KAAK;AACzD,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AAEA,MAAI,OAAO,OAAO;AAChB,WAAO;AAAA,EACT;AAEA,UAAQ,UAAU;AAClB,QAAM,QAAQ,SAAY,KAAK,SAAS,QAAQ;AAEhD,MAAI,CAAC,IAAK,OAAM;AAEhB,MAAI;AACJ,MAAI,OAAO,QAAQ,UAAU;AAC3B,SAAK,IAAI,OAAO,IAAI,KAAK,EAAE,GAAG;AAC5B,WAAK,CAAC,IAAI;AAAA,IACZ;AAAA,EACF,OAAO;AACL,QAAI,QAAQ,iBAAiB,GAAG,IAC5B,MACA,YAAY,IAAI,OAAO,KAAK,QAAQ,EAAE,SAAS,CAAC;AACpD,QAAI,MAAM,MAAM;AAChB,SAAK,IAAI,GAAG,IAAI,MAAM,OAAO,EAAE,GAAG;AAChC,WAAK,IAAI,KAAK,IAAI,MAAM,IAAI,GAAG;AAAA,IACjC;AAAA,EACF;AAEA,SAAO;AACT;AAKA,IAAI,oBAAoB;AAExB,SAAS,YAAa,KAAK;AAEzB,QAAM,WAAW,GAAG,EAAE,QAAQ,mBAAmB,EAAE;AAEnD,MAAI,IAAI,SAAS,EAAG,QAAO;AAE3B,SAAO,IAAI,SAAS,MAAM,GAAG;AAC3B,UAAM,MAAM;AAAA,EACd;AACA,SAAO;AACT;AAEA,SAAS,WAAY,KAAK;AACxB,MAAI,IAAI,KAAM,QAAO,IAAI,KAAK;AAC9B,SAAO,IAAI,QAAQ,cAAc,EAAE;AACrC;AAEA,SAAS,MAAO,GAAG;AACjB,MAAI,IAAI,GAAI,QAAO,MAAM,EAAE,SAAS,EAAE;AACtC,SAAO,EAAE,SAAS,EAAE;AACtB;AAEA,SAAS,YAAa,QAAQ,OAAO;AACnC,UAAQ,SAAS;AACjB,MAAI;AACJ,MAAI,SAAS,OAAO;AACpB,MAAI,gBAAgB;AACpB,MAAI,QAAQ,CAAC;AAEb,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,gBAAY,OAAO,WAAW,CAAC;AAG/B,QAAI,YAAY,SAAU,YAAY,OAAQ;AAE5C,UAAI,CAAC,eAAe;AAElB,YAAI,YAAY,OAAQ;AAEtB,eAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD;AAAA,QACF,WAAW,IAAI,MAAM,QAAQ;AAE3B,eAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD;AAAA,QACF;AAGA,wBAAgB;AAEhB;AAAA,MACF;AAGA,UAAI,YAAY,OAAQ;AACtB,aAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAClD,wBAAgB;AAChB;AAAA,MACF;AAGA,mBAAa,gBAAgB,SAAU,KAAK,YAAY,SAAU;AAAA,IACpE,WAAW,eAAe;AAExB,WAAK,SAAS,KAAK,GAAI,OAAM,KAAK,KAAM,KAAM,GAAI;AAAA,IACpD;AAEA,oBAAgB;AAGhB,QAAI,YAAY,KAAM;AACpB,WAAK,SAAS,KAAK,EAAG;AACtB,YAAM,KAAK,SAAS;AAAA,IACtB,WAAW,YAAY,MAAO;AAC5B,WAAK,SAAS,KAAK,EAAG;AACtB,YAAM;AAAA,QACJ,aAAa,IAAM;AAAA,QACnB,YAAY,KAAO;AAAA,MACrB;AAAA,IACF,WAAW,YAAY,OAAS;AAC9B,WAAK,SAAS,KAAK,EAAG;AACtB,YAAM;AAAA,QACJ,aAAa,KAAM;AAAA,QACnB,aAAa,IAAM,KAAO;AAAA,QAC1B,YAAY,KAAO;AAAA,MACrB;AAAA,IACF,WAAW,YAAY,SAAU;AAC/B,WAAK,SAAS,KAAK,EAAG;AACtB,YAAM;AAAA,QACJ,aAAa,KAAO;AAAA,QACpB,aAAa,KAAM,KAAO;AAAA,QAC1B,aAAa,IAAM,KAAO;AAAA,QAC1B,YAAY,KAAO;AAAA,MACrB;AAAA,IACF,OAAO;AACL,YAAM,IAAI,MAAM,oBAAoB;AAAA,IACtC;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,aAAc,KAAK;AAC1B,MAAI,YAAY,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AAEnC,cAAU,KAAK,IAAI,WAAW,CAAC,IAAI,GAAI;AAAA,EACzC;AACA,SAAO;AACT;AAEA,SAAS,eAAgB,KAAK,OAAO;AACnC,MAAI,GAAG,IAAI;AACX,MAAI,YAAY,CAAC;AACjB,WAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,EAAE,GAAG;AACnC,SAAK,SAAS,KAAK,EAAG;AAEtB,QAAI,IAAI,WAAW,CAAC;AACpB,SAAK,KAAK;AACV,SAAK,IAAI;AACT,cAAU,KAAK,EAAE;AACjB,cAAU,KAAK,EAAE;AAAA,EACnB;AAEA,SAAO;AACT;AAGA,SAAS,cAAe,KAAK;AAC3B,SAAO,YAAY,YAAY,GAAG,CAAC;AACrC;AAEA,SAAS,WAAY,KAAK,KAAK,QAAQ,QAAQ;AAC7C,WAAS,IAAI,GAAG,IAAI,QAAQ,EAAE,GAAG;AAC/B,QAAK,IAAI,UAAU,IAAI,UAAY,KAAK,IAAI,OAAS;AACrD,QAAI,IAAI,MAAM,IAAI,IAAI,CAAC;AAAA,EACzB;AACA,SAAO;AACT;AAEA,SAAS,MAAO,KAAK;AACnB,SAAO,QAAQ;AACjB;AAMA,SAAS,SAAS,KAAK;AACrB,SAAO,OAAO,SAAS,CAAC,CAAC,IAAI,aAAa,aAAa,GAAG,KAAK,aAAa,GAAG;AACjF;AAEA,SAAS,aAAc,KAAK;AAC1B,SAAO,CAAC,CAAC,IAAI,eAAe,OAAO,IAAI,YAAY,aAAa,cAAc,IAAI,YAAY,SAAS,GAAG;AAC5G;AAGA,SAAS,aAAc,KAAK;AAC1B,SAAO,OAAO,IAAI,gBAAgB,cAAc,OAAO,IAAI,UAAU,cAAc,aAAa,IAAI,MAAM,GAAG,CAAC,CAAC;AACjH;AAEA,IAAM,WAAN,MAAM,kBAAiB,MAAM;AAAA,EAC3B,YAAY,MAAM,SAAS,YAAY,UAAU;AAC/C,QAAI,MAAM,QAAQ,OAAO,EAAG,WAAU,QAAQ,KAAK,GAAG,EAAE,KAAK;AAC7D,UAAM,OAAO;AACb,QAAI,MAAM,sBAAsB,QAAW;AACzC,YAAM,kBAAkB,MAAM,SAAQ;AAAA,IACxC;AACA,SAAK,OAAO;AACZ,eAAW,WAAW,UAAU;AAC9B,iBAAW,OAAO,SAAS;AACzB,cAAM,QAAQ,QAAQ,GAAG;AACzB,aAAK,GAAG,IAAI,SAAS,KAAK,IACtB,MAAM,SAAS,QAAQ,QAAQ,IAC/B,SAAS,OACP,QACA,KAAK,MAAM,KAAK,UAAU,KAAK,CAAC;AAAA,MACxC;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,YAAY,SAAU,KAAK;AAC/B,SAAO,OAAO,QAAQ,YAAY,QAAQ,QAAQ,CAAC,MAAM,QAAQ,GAAG;AACtE;AAEA,IAAM,0BAA0B,SAAU,SAAS;AACjD,QAAM,oBAAoB,CAAC;AAC3B,WAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,IAAI,GAAG,KAAK;AAC9C,UAAM,SAAS,QAAQ,CAAC;AACxB,QAAI,WAAW,UAAa,WAAW,QAAQ,WAAW,OAAO;AAC/D,wBAAkB,CAAC,IAAI,EAAE,UAAU,KAAK;AAAA,IAC1C,WAAW,OAAO,WAAW,UAAU;AACrC,wBAAkB,CAAC,IAAI,EAAE,MAAM,OAAO;AAAA,IACxC,WAAW,UAAU,MAAM,GAAG;AAC5B,UAAI,OAAO,OAAO,SAAS,UAAU;AACnC,cAAM,IAAI,SAAS,mCAAmC;AAAA,UACpD;AAAA,UACA,2CAA2C,CAAC;AAAA,UAC5C;AAAA,QACF,CAAC;AAAA,MACH;AACA,wBAAkB,CAAC,IAAI;AAAA,IACzB,OAAO;AACL,YAAM,IAAI,SAAS,iCAAiC;AAAA,QAClD;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,MAAM,CAAC,gBAAgB,CAAC;AAAA,MAChD,CAAC;AAAA,IACH;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,mBAAN,MAAuB;AAAA,EACrB,YAAY,OAAO,KAAK;AACtB,SAAK,OAAO;AACZ,SAAK,SAAS;AACd,SAAK,MAAM,OAAO,YAAY,IAAI;AAAA,EACpC;AAAA,EACA,QAAQ,KAAK;AACX,QAAI,SAAS,GAAG,GAAG;AACjB,YAAM,SAAS,KAAK,SAAS,IAAI;AACjC,UAAI,UAAU,KAAK,MAAM;AACvB,aAAK,OAAO;AACZ,YAAI,UAAU,KAAK,MAAM;AACvB,gBAAM,MAAM,sBAAsB;AAAA,QACpC;AAAA,MACF;AACA,YAAM,MAAM,KAAK;AACjB,WAAK,MAAM,OAAO,YAAY,KAAK,IAAI;AACvC,UAAI,KAAK,KAAK,KAAK,CAAC;AACpB,UAAI,KAAK,KAAK,KAAK,IAAI,MAAM;AAC7B,WAAK,UAAU,IAAI;AAAA,IACrB,OAAO;AACL,YAAM,SAAS,KAAK;AACpB,UAAI,WAAW,KAAK,MAAM;AACxB,aAAK,OAAO;AAAA,MACd;AACA,YAAM,MAAM,KAAK,MAAM;AACvB,WAAK,IAAI,CAAC,IAAI;AACd,UAAI,KAAK,KAAK,KAAK,GAAG,GAAG,MAAM;AAAA,IACjC;AAAA,EACF;AAAA,EACA,OAAO,KAAK;AACV,UAAM,SAAS,KAAK;AACpB,QAAI,WAAW,KAAK,MAAM;AACxB,WAAK,OAAO;AAAA,IACd;AACA,SAAK,IAAI,MAAM,IAAI;AAAA,EACrB;AAAA,EACA,QAAQ;AACN,WAAO,OAAO,KAAK,KAAK,IAAI,MAAM,GAAG,KAAK,MAAM,CAAC;AAAA,EACnD;AAAA,EACA,SAAS;AACP,UAAM,SAAS,KAAK;AACpB,SAAK,OAAO,KAAK,OAAO;AACxB,UAAM,MAAM,OAAO,YAAY,KAAK,IAAI;AACxC,SAAK,IAAI,KAAK,KAAK,GAAG,GAAG,MAAM;AAC/B,SAAK,MAAM;AAAA,EACb;AAAA,EACA,SAAS,UAAU;AACjB,QAAI,UAAU;AACZ,aAAO,KAAK,IAAI,MAAM,GAAG,KAAK,MAAM,EAAE,SAAS,QAAQ;AAAA,IACzD,OAAO;AACL,aAAO,WAAW,UAAU,MAAM,KAAK,KAAK,IAAI,MAAM,GAAG,KAAK,MAAM,CAAC;AAAA,IACvE;AAAA,EACF;AAAA,EACA,SAAS;AACP,WAAO,KAAK,SAAS,MAAM;AAAA,EAC7B;AAAA,EACA,QAAQ;AACN,SAAK,SAAS;AAAA,EAChB;AACF;AAMA,IAAM,KAAK;AACX,IAAM,OAAO;AACb,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,MAAM;AAEZ,IAAM,aAAa,SAAU,SAAS;AACpC,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,eAAe;AAAA,IACf,WAAW,QAAQ;AAAA,IACnB,YAAY;AAAA;AAAA,IAEZ,OAAO;AAAA,IACP,SAAS,QAAQ,cAAc;AAAA,IAC/B,UAAU;AAAA,IACV,eACE,SAAS,QAAQ,MAAM,KACvB,SAAS,QAAQ,KAAK,KACtB,OAAO,QAAQ,QAAQ,QAAQ,QAAQ,KAAK,MAAM;AAAA;AAAA,IAEpD,sBAAsB,MAAM,QAAQ,QAAQ,OAAO,IAC/C,QAAQ,QAAQ,SAChB;AAAA,IACJ,OAAO,IAAI,iBAAiB,EAAE;AAAA,IAC9B,oBAAoB,QAAQ;AAAA,IAC5B,kBAAkB,KAAK;AAAA;AAAA,MAErB,QAAQ,YAAY,OAAO,QAAQ,QAAQ,SAAS;AAAA,MAEpD,GAAG,QAAQ,UAAU,IAAI,CAAC,cAAc,UAAU,MAAM;AAAA;AAAA,MAExD,QAAQ,UAAU,OAAO,QAAQ,MAAM,SAAS;AAAA,IAClD;AAAA,IACA,aAAa;AAAA,IACb,SAAS;AAAA,IACT,MAAM;AAAA,IACN,WAAW,IAAI,iBAAiB,GAAG;AAAA,IACnC,QAAQ,CAAC;AAAA,IACT,gBAAgB;AAAA,IAChB,eAAe;AAAA,IACf,0BACE,QAAQ,iBAAiB,WAAW,IAChC,IACA,KAAK,IAAI,GAAG,QAAQ,iBAAiB,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC;AAAA,IAC/D,WAAW;AAAA,MACT,OAAO,KAAK,KAAK,QAAQ,QAAQ,EAAE,CAAC;AAAA,MACpC,OAAO,KAAK,KAAM,QAAQ,QAAQ,EAAE,CAAC;AAAA,IACvC;AAAA,IACA,YAAY;AAAA,IACZ,iBAAiB;AAAA,IACjB,UAAU;AAAA,MACR,OAAO,KAAK,OAAO,KAAK,CAAC,IAAI,GAAG,MAAM,EAAE,SAAS,GAAG,QAAQ,QAAQ;AAAA,MACpE,OAAO,KAAK,OAAO,KAAK,CAAC,IAAI,GAAG,MAAM,EAAE,SAAS,GAAG,QAAQ,QAAQ;AAAA,MACpE,OAAO,KAAK,OAAO,KAAK,CAAC,EAAE,GAAG,MAAM,EAAE,SAAS,GAAG,QAAQ,QAAQ;AAAA,MAClE,OAAO,KAAK,OAAO,KAAK,CAAC,KAAK,GAAG,MAAM,EAAE,SAAS,GAAG,QAAQ,QAAQ;AAAA,MACrE,OAAO,KAAK,OAAO,KAAK,CAAC,GAAG,GAAG,MAAM,EAAE,SAAS,GAAG,QAAQ,QAAQ;AAAA,IACrE;AAAA,EACF;AACF;AAEA,IAAM,aAAa,SAAU,KAAK;AAChC,SAAO,IAAI,QAAQ,YAAY,SAAU,GAAG,OAAO;AACjD,WAAO,MAAM,MAAM,YAAY;AAAA,EACjC,CAAC;AACH;AAEA,IAAM,oBAAoB,SAAU,MAAM;AACxC,QAAM,UAAU,CAAC;AAEjB,aAAW,OAAO,MAAM;AACtB,YAAQ,WAAW,GAAG,CAAC,IAAI,KAAK,GAAG;AAAA,EACrC;AAIA,MAAI,QAAQ,aAAa,UAAa,QAAQ,aAAa,MAAM;AAC/D,YAAQ,WAAW;AAAA,EACrB,WAAW,QAAQ,aAAa,QAAQ,QAAQ,aAAa,OAAO;AAClE,YAAQ,WAAW;AAAA,EACrB,WACE,OAAO,QAAQ,aAAa,YAC5B,QAAQ,aAAa,MACrB;AACA,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,QAAQ,CAAC;AAAA,MACzC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,QAAQ,UAChB,QAAQ,QAAQ,QAChB,QAAQ,QAAQ,OAChB;AACA,YAAQ,MAAM;AAAA,EAChB,WAAW,QAAQ,QAAQ,MAAM;AAC/B,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,GAAG,CAAC;AAAA,MACpC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,UAAQ,gBAAgB;AACxB,MACE,QAAQ,SAAS,UACjB,QAAQ,SAAS,QACjB,QAAQ,SAAS,SACjB,QAAQ,SAAS,IACjB;AACA,YAAQ,OAAO;AAAA,EACjB,WAAW,OAAO,QAAQ,SAAS,YAAY;AAC7C,YAAQ,gBAAgB,QAAQ;AAChC,YAAQ,OAAO;AAAA,EACjB,WAAW,QAAQ,SAAS,MAAM;AAChC,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,IAAI,CAAC;AAAA,MACrC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,cAAc,UACtB,QAAQ,cAAc,QACtB,QAAQ,cAAc,SACtB,QAAQ,cAAc,IACtB;AACA,YAAQ,YAAY;AAAA,EACtB,WAAW,QAAQ,cAAc,MAAM;AACrC,YAAQ,YAAY,SAAU,OAAO;AACnC,YAAM,OAAO,KAAK,MAAM,KAAK;AAC7B,aAAO,CAAC,MAAM,IAAI,IAAI,IAAI,KAAK,IAAI,IAAI;AAAA,IACzC;AAAA,EACF,WAAW,OAAO,QAAQ,cAAc,YAAY;AAClD,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,SAAS,CAAC;AAAA,MAC1C;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,UAAQ,4BAA4B;AACpC,MAAI,QAAQ,YAAY,MAAM;AAE5B,YAAQ,4BAA4B;AAAA,EACtC,WAAW,OAAO,QAAQ,YAAY,YAAY;AAChD,YAAQ,4BAA4B,QAAQ;AAC5C,YAAQ,UAAU;AAAA,EACpB,WAAW,MAAM,QAAQ,QAAQ,OAAO,GAAG;AACzC,YAAQ,UAAU,wBAAwB,QAAQ,OAAO;AAAA,EAC3D,WACE,QAAQ,YAAY,UACpB,QAAQ,YAAY,QACpB,QAAQ,YAAY,OACpB;AACA,YAAQ,UAAU;AAAA,EACpB,OAAO;AACL,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,OAAO,CAAC;AAAA,MACxC;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,0BAA0B,UAClC,QAAQ,0BAA0B,QAClC,QAAQ,0BAA0B,OAClC;AACA,YAAQ,wBAAwB;AAAA,EAClC,WAAW,QAAQ,0BAA0B,MAAM;AACjD,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,qBAAqB,CAAC;AAAA,MACtD;AAAA,MACA;AAAA,IACF;AAAA,EACF,WAAW,QAAQ,YAAY,OAAO;AACpC,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,YAAY,UACpB,QAAQ,YAAY,QACpB,QAAQ,YAAY,SACpB,QAAQ,YAAY,IACpB;AACA,YAAQ,UAAU;AAAA,EACpB,OAAO;AACL,QAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,cAAQ,UAAU,OAAO,KAAK,QAAQ,SAAS,QAAQ,QAAQ;AAAA,IACjE;AACA,QAAI,CAAC,SAAS,QAAQ,OAAO,GAAG;AAC9B,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,OAAO,KAAK,UAAU,QAAQ,OAAO,CAAC;AAAA,QACxC;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,qBAAqB,UAC7B,QAAQ,qBAAqB,QAC7B,QAAQ,qBAAqB,OAC7B;AACA,YAAQ,mBAAmB;AAAA,EAC7B,WAAW,QAAQ,qBAAqB,MAAM;AAC5C,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,gBAAgB,CAAC;AAAA,MACjD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,QAAM,iBAAiB,KAAK,UAAU,QAAQ,SAAS;AACvD,MAAI,CAAC,MAAM,QAAQ,QAAQ,SAAS;AAClC,YAAQ,YAAY,CAAC,QAAQ,SAAS;AACxC,MAAI,QAAQ,UAAU,WAAW,GAAG;AAClC,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,cAAc;AAAA,MACvB;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,UAAQ,YAAY,QAAQ,UAAU,IAAI,SAAU,WAAW;AAC7D,QAAI,cAAc,UAAa,cAAc,QAAQ,cAAc,OAAO;AACxE,aAAO,OAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,IAC1C;AACA,QAAI,OAAO,cAAc,UAAU;AACjC,kBAAY,OAAO,KAAK,WAAW,QAAQ,QAAQ;AAAA,IACrD;AACA,QAAI,CAAC,SAAS,SAAS,KAAK,UAAU,WAAW,GAAG;AAClD,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,OAAO,cAAc;AAAA,QACvB;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT,CAAC;AAED,MAAI,QAAQ,WAAW,UAAa,QAAQ,WAAW,MAAM;AAC3D,YAAQ,SAAS,OAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,EACpD,WAAW,OAAO,QAAQ,WAAW,UAAU;AAC7C,YAAQ,SAAS,OAAO,KAAK,QAAQ,QAAQ,QAAQ,QAAQ;AAAA,EAC/D,WAAW,QAAQ,WAAW,QAAQ,QAAQ,WAAW,OAAO;AAC9D,YAAQ,SAAS;AAAA,EACnB;AACA,MAAI,QAAQ,WAAW,MAAM;AAC3B,QAAI,CAAC,SAAS,QAAQ,MAAM,GAAG;AAC7B,YAAM,IAAI;AAAA,QACR,uEAAuE,KAAK,UAAU,QAAQ,MAAM,CAAC;AAAA,MACvG;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,SAAS,UAAa,QAAQ,SAAS,MAAM;AACvD,YAAQ,OAAO;AAAA,EACjB,OAAO;AACL,QAAI,OAAO,QAAQ,SAAS,YAAY,MAAM,KAAK,QAAQ,IAAI,GAAG;AAChE,cAAQ,OAAO,SAAS,QAAQ,IAAI;AAAA,IACtC;AACA,QAAI,OAAO,UAAU,QAAQ,IAAI,GAAG;AAClC,UAAI,QAAQ,OAAO,GAAG;AACpB,cAAM,IAAI;AAAA,UACR,wDAAwD,KAAK,UAAU,KAAK,IAAI,CAAC;AAAA,QACnF;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI;AAAA,QACR,gDAAgD,KAAK,UAAU,QAAQ,IAAI,CAAC;AAAA,MAC9E;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,cAAc,UAAa,QAAQ,cAAc,MAAM;AACjE,YAAQ,YAAY;AAAA,EACtB,OAAO;AACL,QACE,OAAO,QAAQ,cAAc,YAC7B,MAAM,KAAK,QAAQ,SAAS,GAC5B;AACA,cAAQ,YAAY,SAAS,QAAQ,SAAS;AAAA,IAChD;AACA,QAAI,OAAO,UAAU,QAAQ,SAAS,GAAG;AACvC,UAAI,QAAQ,aAAa,GAAG;AAC1B,cAAM,IAAI;AAAA,UACR,4EAA4E,KAAK,UAAU,KAAK,SAAS,CAAC;AAAA,QAC5G;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI;AAAA,QACR,qDAAqD,KAAK,UAAU,KAAK,SAAS,CAAC;AAAA,MACrF;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,2BAA2B,UACnC,QAAQ,2BAA2B,MACnC;AACA,YAAQ,yBAAyB;AAAA,EACnC,WAAW,OAAO,QAAQ,2BAA2B,UAAU;AAC7D,YAAQ,yBAAyB,KAAK,MAAM,QAAQ,sBAAsB;AAC1E,QAAI,QAAQ,2BAA2B,GAAG;AACxC,cAAQ,yBAAyB;AAAA,IACnC;AAAA,EACF,WAAW,OAAO,QAAQ,2BAA2B,WAAW;AAC9D,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,sBAAsB,CAAC;AAAA,MACvD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,MAAI,QAAQ,2BAA2B,QAAQ,QAAQ,YAAY,OAAO;AACxE,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,MACF;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,SAAS,UACjB,QAAQ,SAAS,QACjB,QAAQ,SAAS,OACjB;AACA,YAAQ,OAAO;AAAA,EACjB,WAAW,QAAQ,SAAS,MAAM;AAChC,UAAM,IAAI;AAAA,MACR,0CAA0C,KAAK,UAAU,QAAQ,IAAI,CAAC;AAAA,IACxE;AAAA,EACF;AAEA,MACE,QAAQ,oBAAoB,UAC5B,QAAQ,oBAAoB,QAC5B,QAAQ,oBAAoB,OAC5B;AACA,YAAQ,kBAAkB;AAAA,EAC5B,WACE,OAAO,UAAU,QAAQ,eAAe,KACxC,QAAQ,mBAAmB,EAC3B;AAAA,WACA,OAAO,QAAQ,oBAAoB,YACnC,MAAM,KAAK,QAAQ,eAAe,GAClC;AACA,YAAQ,kBAAkB,SAAS,QAAQ,eAAe;AAAA,EAC5D,OAAO;AACL,UAAM,IAAI;AAAA,MACR,mEAAmE,KAAK,UAAU,QAAQ,eAAe,CAAC;AAAA,IAC5G;AAAA,EACF;AAEA,MACE,QAAQ,YAAY,UACpB,QAAQ,YAAY,QACpB,QAAQ,YAAY,OACpB;AACA,YAAQ,UAAU;AAAA,EACpB,WAAW,SAAS,QAAQ,OAAO,GAAG;AACpC,QAAI,QAAQ,QAAQ,WAAW,GAAG;AAChC,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACtE;AACA,QAAI,QAAQ,aAAa,KAAM;AAAA,SAAO;AACpC,cAAQ,UAAU,QAAQ,QAAQ,SAAS,QAAQ,QAAQ;AAAA,IAC7D;AAAA,EACF,WAAW,OAAO,QAAQ,YAAY,UAAU;AAC9C,QAAI,QAAQ,QAAQ,WAAW,GAAG;AAChC,YAAM,IAAI,MAAM,oDAAoD;AAAA,IACtE;AAAA,EAEF,WAAW,OAAO,QAAQ,YAAY,SAAU;AAAA,OAAO;AACrD,UAAM,IAAI;AAAA,MACR,6DAA6D,QAAQ,OAAO;AAAA,IAC9E;AAAA,EACF;AACA,MAAI,QAAQ,YAAY,QAAW;AACjC,QAAI,OAAO,QAAQ,YAAY,UAAU;AACvC,UAAI,QAAQ,YAAY,OAAO;AAC7B,cAAM;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AAEL,UAAI,QAAQ,YAAY,OAAO;AAC7B,cAAM;AAAA,UACJ;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,cAAc,UAAa,QAAQ,cAAc,MAAM;AACjE,YAAQ,YAAY;AAAA,EACtB,WAAW,OAAO,QAAQ,cAAc,YAAY;AAClD,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,SAAS,CAAC;AAAA,MAC1C;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAKA,MACE,QAAQ,YAAY,UACpB,QAAQ,YAAY,QACpB,OAAO,QAAQ,YAAY,YAC3B;AACA,UAAM,IAAI;AAAA,MACR,mDAAmD,KAAK,UAAU,QAAQ,OAAO,CAAC;AAAA,IACpF;AAAA,EACF;AAEA,MACE,QAAQ,UAAU,QAClB,QAAQ,UAAU,SAClB,QAAQ,UAAU,IAClB;AACA,YAAQ,QAAQ;AAAA,EAClB,OAAO;AACL,QAAI,QAAQ,UAAU,UAAa,QAAQ,UAAU,MAAM;AACzD,cAAQ,QAAQ,OAAO,KAAK,KAAK,QAAQ,QAAQ;AAAA,IACnD,WAAW,OAAO,QAAQ,UAAU,UAAU;AAC5C,cAAQ,QAAQ,OAAO,KAAK,QAAQ,OAAO,QAAQ,QAAQ;AAAA,IAC7D;AACA,QAAI,CAAC,SAAS,QAAQ,KAAK,GAAG;AAC5B,YAAM,IAAI;AAAA,QACR,2DAA2D,KAAK,UAAU,QAAQ,KAAK,CAAC;AAAA,MAC1F;AAAA,IACF;AAAA,EACF;AAEA,MACE,QAAQ,QAAQ,UAChB,QAAQ,QAAQ,QAChB,QAAQ,QAAQ,OAChB;AACA,YAAQ,MAAM;AAAA,EAChB,WAAW,QAAQ,QAAQ,MAAM;AAC/B,UAAM,IAAI;AAAA,MACR,yCAAyC,KAAK,UAAU,QAAQ,GAAG,CAAC;AAAA,IACtE;AAAA,EACF;AAEA,MAAI,QAAQ,qBAAqB,QAAW;AAC1C,YAAQ,mBAAmB,CAAC;AAAA,EAC9B,WACE,OAAO,QAAQ,qBAAqB,YACpC,SAAS,QAAQ,gBAAgB,GACjC;AACA,QAAI,QAAQ,iBAAiB,WAAW,GAAG;AACzC,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,OAAO,KAAK,UAAU,QAAQ,gBAAgB,CAAC;AAAA,QACjD;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,YAAQ,mBAAmB,CAAC,QAAQ,gBAAgB;AAAA,EACtD,WAAW,CAAC,MAAM,QAAQ,QAAQ,gBAAgB,GAAG;AACnD,UAAM,IAAI;AAAA,MACR;AAAA,MACA;AAAA,QACE;AAAA,QACA;AAAA,QACA,OAAO,KAAK,UAAU,QAAQ,gBAAgB,CAAC;AAAA,MACjD;AAAA,MACA;AAAA,IACF;AAAA,EACF;AACA,UAAQ,mBAAmB,QAAQ,iBAAiB,IAAI,SAAU,IAAI,GAAG;AACvE,QAAI,OAAO,OAAO,YAAY,CAAC,SAAS,EAAE,GAAG;AAC3C,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,YAAY,CAAC;AAAA,UACb,OAAO,KAAK,UAAU,EAAE,CAAC;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AAAA,IACF,WAAW,GAAG,WAAW,GAAG;AAC1B,YAAM,IAAI;AAAA,QACR;AAAA,QACA;AAAA,UACE;AAAA,UACA;AAAA,UACA,YAAY,CAAC;AAAA,UACb,OAAO,KAAK,UAAU,EAAE,CAAC;AAAA,QAC3B;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,OAAO,OAAO,UAAU;AAC1B,WAAK,OAAO,KAAK,IAAI,QAAQ,QAAQ;AAAA,IACvC;AACA,WAAO;AAAA,EACT,CAAC;AAED,MAAI,OAAO,QAAQ,uBAAuB,UAAW;AAAA,WACnD,QAAQ,uBAAuB,UAC/B,QAAQ,uBAAuB,MAC/B;AACA,YAAQ,qBAAqB;AAAA,EAC/B,OAAO;AACL,UAAM,IAAI;AAAA,MACR,6DAA6D,KAAK,UAAU,QAAQ,kBAAkB,CAAC;AAAA,IACzG;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,4BAA4B,UAAW;AAAA,WACxD,QAAQ,4BAA4B,UACpC,QAAQ,4BAA4B,MACpC;AACA,YAAQ,0BAA0B;AAAA,EACpC,OAAO;AACL,UAAM,IAAI;AAAA,MACR,kEAAkE,KAAK,UAAU,QAAQ,uBAAuB,CAAC;AAAA,IACnH;AAAA,EACF;AACA,MAAI,OAAO,QAAQ,4BAA4B,UAAW;AAAA,WACxD,QAAQ,4BAA4B,UACpC,QAAQ,4BAA4B,MACpC;AACA,YAAQ,0BAA0B;AAAA,EACpC,OAAO;AACL,UAAM,IAAI;AAAA,MACR,kEAAkE,KAAK,UAAU,QAAQ,uBAAuB,CAAC;AAAA,IACnH;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,iBAAiB,UAAW;AAAA,WAC7C,QAAQ,iBAAiB,UACzB,QAAQ,iBAAiB,MACzB;AACA,YAAQ,eAAe;AAAA,EACzB,OAAO;AACL,UAAM,IAAI;AAAA,MACR,uDAAuD,KAAK,UAAU,QAAQ,YAAY,CAAC;AAAA,IAC7F;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,qBAAqB,UAAW;AAAA,WACjD,QAAQ,qBAAqB,UAC7B,QAAQ,qBAAqB,MAC7B;AACA,YAAQ,mBAAmB;AAAA,EAC7B,OAAO;AACL,UAAM,IAAI;AAAA,MACR,2DAA2D,KAAK,UAAU,QAAQ,gBAAgB,CAAC;AAAA,IACrG;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,mCAAmC,UAAW;AAAA,WAC/D,QAAQ,mCAAmC,UAC3C,QAAQ,mCAAmC,MAC3C;AACA,YAAQ,iCAAiC;AAAA,EAC3C,OAAO;AACL,UAAM,IAAI;AAAA,MACR,yEAAyE,KAAK,UAAU,QAAQ,8BAA8B,CAAC;AAAA,IACjI;AAAA,EACF;AAEA,MAAI,OAAO,QAAQ,4BAA4B,UAAW;AAAA,WACxD,QAAQ,4BAA4B,UACpC,QAAQ,4BAA4B,MACpC;AACA,YAAQ,0BAA0B;AAAA,EACpC,OAAO;AACL,UAAM,IAAI;AAAA,MACR,kEAAkE,KAAK,UAAU,QAAQ,uBAAuB,CAAC;AAAA,IACnH;AAAA,EACF;AAEA,MACE,QAAQ,UAAU,UAClB,QAAQ,UAAU,QAClB,QAAQ,UAAU,OAClB;AACA,YAAQ,QAAQ;AAAA,EAClB,WAAW,QAAQ,UAAU,MAAM;AACjC,UAAM,IAAI;AAAA,MACR,gDAAgD,KAAK,UAAU,QAAQ,KAAK,CAAC;AAAA,IAC/E;AAAA,EACF;AAEA,MACE,QAAQ,UAAU,UAClB,QAAQ,UAAU,QAClB,QAAQ,UAAU,OAClB;AACA,YAAQ,QAAQ;AAAA,EAClB,WAAW,QAAQ,UAAU,MAAM;AACjC,UAAM,IAAI;AAAA,MACR,gDAAgD,KAAK,UAAU,QAAQ,KAAK,CAAC;AAAA,IAC/E;AAAA,EACF;AAEA,MACE,QAAQ,SAAS,UACjB,QAAQ,SAAS,QACjB,QAAQ,SAAS,OACjB;AACA,YAAQ,OAAO;AAAA,EACjB,WAAW,QAAQ,SAAS,MAAM;AAChC,UAAM,IAAI;AAAA,MACR,+CAA+C,KAAK,UAAU,QAAQ,IAAI,CAAC;AAAA,IAC7E;AAAA,EACF;AAEA,MAAI,QAAQ,SAAS,QAAQ,KAAK,UAAU,OAAO;AACjD,YAAQ,QAAQ;AAAA,EAClB,WAAW,QAAQ,UAAU,MAAM;AACjC,YAAQ,QAAQ;AAAA,EAClB;AACA,MAAI,QAAQ,SAAS,QAAQ,KAAK,UAAU,OAAO;AACjD,YAAQ,QAAQ;AAAA,EAClB,WAAW,QAAQ,UAAU,MAAM;AACjC,YAAQ,QAAQ;AAAA,EAClB;AAEA,MAAI,QAAQ,OAAO,UAAa,QAAQ,OAAO,MAAM;AACnD,YAAQ,KAAK;AAAA,EACf,OAAO;AACL,QAAI,OAAO,QAAQ,OAAO,YAAY,MAAM,KAAK,QAAQ,EAAE,GAAG;AAC5D,cAAQ,KAAK,SAAS,QAAQ,EAAE;AAAA,IAClC;AACA,QAAI,OAAO,UAAU,QAAQ,EAAE,GAAG;AAChC,UAAI,QAAQ,MAAM,GAAG;AACnB,cAAM,IAAI;AAAA,UACR,qEAAqE,KAAK,UAAU,KAAK,EAAE,CAAC;AAAA,QAC9F;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI;AAAA,QACR,8CAA8C,KAAK,UAAU,KAAK,EAAE,CAAC;AAAA,MACvE;AAAA,IACF;AAAA,EACF;AAEA,MAAI,QAAQ,YAAY,UAAa,QAAQ,YAAY,MAAM;AAC7D,YAAQ,UAAU;AAAA,EACpB,OAAO;AACL,QAAI,OAAO,QAAQ,YAAY,YAAY,MAAM,KAAK,QAAQ,OAAO,GAAG;AACtE,cAAQ,UAAU,SAAS,QAAQ,OAAO;AAAA,IAC5C;AACA,QAAI,OAAO,UAAU,QAAQ,OAAO,GAAG;AACrC,UAAI,QAAQ,WAAW,GAAG;AACxB,cAAM,IAAI;AAAA,UACR,0EAA0E,KAAK,UAAU,KAAK,OAAO,CAAC;AAAA,QACxG;AAAA,MACF;AAAA,IACF,OAAO;AACL,YAAM,IAAI;AAAA,QACR,mDAAmD,KAAK,UAAU,KAAK,OAAO,CAAC;AAAA,MACjF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAEA,IAAM,gBAAgB,SAAU,QAAQ;AACtC,SAAO,OAAO;AAAA,IACZ,CAAC,UACC,SAAS,QAAS,MAAM,YAAY,MAAM,SAAS,EAAE,KAAK,MAAM;AAAA,EACpE;AACF;AAEA,IAAM,KAAK;AACX,IAAM,KAAK;AAEX,IAAM,OAAO;AAAA;AAAA;AAAA;AAAA;AAAA,EAKX,MAAM,OAAO,KAAK,CAAC,KAAK,KAAK,GAAG,CAAC;AAAA;AAAA;AAAA;AAAA,EAIjC,SAAS,OAAO,KAAK,CAAC,KAAK,GAAG,CAAC;AACjC;AAEA,IAAM,YAAY,SAAU,mBAAmB,CAAC,GAAG;AACjD,QAAM,OAAO;AAAA,IACX,OAAO;AAAA,IACP,eAAe;AAAA,IACf,aAAa;AAAA,IACb,sBAAsB;AAAA,IACtB,OAAO;AAAA,IACP,SAAS;AAAA,EACX;AACA,QAAM,UAAU,kBAAkB,gBAAgB;AAClD,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA,OAAO,WAAW,OAAO;AAAA,IACzB,gBAAgB,SAAU,GAAG,QAAQ,KAAK;AACxC,UAAI,IAAK,QAAO;AAChB,YAAM,EAAE,UAAU,QAAQ,MAAM,IAAI,KAAK;AACzC,YAAM,EAAE,SAAS,kBAAkB,yBAAyB,IAC1D,KAAK;AACP,YAAM,gBAAgB,SAAS,IAAI;AACnC,YAAM,iBAAiB,KAAK;AAAA,QAC1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAOA,6BAA6B,IACzB,OAAO,KAAK,QAAQ,QAAQ,EAAE,SAC9B;AAAA;AAAA,QAEJ,WAAW,WAAW,OAAO,IAAI,OAAO,UAAU,MAAM,SAAS;AAAA;AAAA,QAEjE,UAAU,MAAM,SAAS,2BAA2B;AAAA,MACtD;AACA,aAAO,gBAAgB;AAAA,IACzB;AAAA;AAAA,IAEA,OAAO,SAAU,SAAS,KAAK,MAAM,OAAO;AAC1C,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,UAAI,EAAE,SAAS,QAAQ,OAAO,iBAAiB,IAAI,KAAK;AACxD,YAAM,EAAE,YAAY,aAAa,WAAW,cAAc,IAAI,KAAK;AACnE,UAAI;AACJ,UAAI,gBAAgB,QAAW;AAC7B,YAAI,YAAY,QAAW;AAEzB,gBAAM;AACN;AAAA,QACF,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF,WAAW,gBAAgB,UAAa,YAAY,QAAW;AAC7D,cAAM;AAAA,MACR,OAAO;AACL,cAAM,OAAO,OAAO,CAAC,aAAa,OAAO,CAAC;AAAA,MAC5C;AAEA,UAAI,eAAe,OAAO;AACxB,YAAI,QAAQ,OAAO;AACjB,eAAK,MAAM,aAAa;AAAA,QAC1B,WAAW,IAAI,SAAS,GAAG;AAEzB,cAAI,QAAQ,OAAO;AAEjB,iBAAK,MAAM,cAAc;AACzB;AAAA,UACF;AAAA,QACF,OAAO;AACL,qBAAWC,aAAY,MAAM;AAC3B,gBAAI,KAAKA,SAAQ,EAAE,QAAQ,KAAK,GAAG,KAAKA,SAAQ,EAAE,MAAM,MAAM,GAAG;AAE/D,oBAAM,YAAY,KAAKA,SAAQ,EAAE;AACjC,mBAAK,MAAM,iBAAiB;AAC5B,oBAAM,IAAI,MAAM,SAAS;AAEzB,mBAAK,UAAU,kBAAkB;AAAA,gBAC/B,GAAG,KAAK;AAAA,gBACR,UAAUA;AAAA,cACZ,CAAC;AAED,eAAC,EAAE,SAAS,QAAQ,MAAM,IAAI,KAAK;AACnC;AAAA,YACF;AAAA,UACF;AACA,eAAK,MAAM,aAAa;AAAA,QAC1B;AAAA,MACF;AACA,YAAM,SAAS,IAAI;AACnB,UAAI;AACJ,WAAK,MAAM,GAAG,MAAM,QAAQ,OAAO;AAGjC,YAAI,KAAK,eAAe,KAAK,QAAQ,GAAG,GAAG;AACzC;AAAA,QACF;AACA,YAAI,KAAK,MAAM,oBAAoB,MAAM;AACvC,eAAK,KAAK;AACV,eAAK,MAAM,kBAAkB;AAAA,QAC/B;AACA,YAAI,YAAY,MAAM,KAAK,KAAK,QAAQ,SAAS;AAC/C,eAAK,MAAM,OAAO;AAClB,gBAAM;AACN;AAAA,QACF;AAEA,YAAI,KAAK,MAAM,YAAY,SAAS,iBAAiB,WAAW,GAAG;AACjE,gBAAM,wBAAwB,KAAK;AAAA,YACjC;AAAA,YACA;AAAA,UACF;AACA,cAAI,uBAAuB;AACzB,+BAAmB,KAAK,QAAQ;AAAA,UAClC;AAAA,QACF;AACA,cAAM,MAAM,IAAI,GAAG;AACnB,YAAI,QAAQ,MAAM;AAChB,oBAAU,OAAO,GAAG;AAAA,QACtB;AACA,aACG,QAAQ,MAAM,QAAQ,OACvB,KAAK,MAAM,oBAAoB,OAC/B;AACA,eAAK,MAAM,kBAAkB;AAAA,QAC/B;AAGA,YAAI,KAAK,MAAM,aAAa,MAAM;AAChC,eAAK,MAAM,WAAW;AAAA,QACxB,OAAO;AAIL,cACE,WAAW,QACX,KAAK,MAAM,YAAY,QACvB,KAAK,WAAW,KAAK,KAAK,GAAG,KAC7B,MAAM,OAAO,SAAS,QACtB;AACA,gBAAI,eAAe;AACjB,kBAAI,KAAK,UAAU,KAAK,MAAM,OAAO,MAAM,GAAG;AAC5C,qBAAK,MAAM,WAAW;AACtB,uBAAO,OAAO,SAAS;AACvB;AAAA,cACF;AAAA,YACF,OAAO;AACL,mBAAK,MAAM,WAAW;AACtB,qBAAO,OAAO,SAAS;AACvB;AAAA,YACF;AAAA,UACF;AAGA,cAAI,KAAK,MAAM,eAAe,SAAS,KAAK,UAAU,KAAK,GAAG,GAAG;AAC/D,gBAAI,KAAK,MAAM,YAAY,MAAM;AAC/B,oBAAM,UAAU,IAAI,MAAM,MAAM,MAAM;AACtC,oBAAM,oBACJ,SAAS,KAAK,iBAAiB,KAAK,MAAM,MAAM,MAAM;AACxD,oBAAM,mBACJ,YAAY,QACZ,KAAK,eAAe,SAAS,KAAK,MAAM,MAAM,QAAQ,OAAO;AAC/D,oBAAM,qBAAqB,KAAK;AAAA,gBAC9B;AAAA,gBACA,MAAM,MAAM;AAAA,gBACZ;AAAA,cACF;AACA,oBAAM,2BACJ,iBAAiB,WAAW,IACxB,KAAK,8BAA8B,KAAK,MAAM,MAAM,MAAM,IAC1D,KAAK,oBAAoB,SAAS,KAAK,MAAM,MAAM,MAAM;AAG/D,kBACE,WAAW,QACX,KAAK,WAAW,KAAK,KAAK,GAAG,KAC7B,KAAK,UAAU,KAAK,MAAM,OAAO,MAAM,GACvC;AACA,uBAAO,OAAO,SAAS;AAAA,cACzB,WACE,CAAC,WACD,sBACA,4BACA,oBACA,mBACA;AACA,qBAAK,MAAM,UAAU;AACrB,qBAAK,MAAM,aAAa;AACxB,uBAAO,MAAM,SAAS;AACtB;AAAA,cACF,WAAW,iBAAiB,OAAO;AACjC,sBAAM,MAAM,KAAK;AAAA,kBACf,IAAI;AAAA,oBACF;AAAA,oBACA;AAAA,sBACE;AAAA,sBACA,QAAQ,OAAO,aAAa,OAAO,CAAC;AAAA,sBACpC,WAAW,KAAK,KAAK,KAAK;AAAA,sBAC1B;AAAA,sBACA;AAAA,oBACF;AAAA,oBACA,KAAK;AAAA,oBACL,KAAK,YAAY;AAAA,kBACnB;AAAA,gBACF;AACA,oBAAI,QAAQ,OAAW,QAAO;AAAA,cAChC,OAAO;AACL,qBAAK,MAAM,UAAU;AACrB,qBAAK,MAAM,aAAa;AACxB,qBAAK,MAAM,MAAM,QAAQ,KAAK;AAC9B,uBAAO,MAAM,SAAS;AAAA,cACxB;AAAA,YACF,OAAO;AACL,kBAAI,KAAK,MAAM,MAAM,WAAW,GAAG;AAEjC,oBAAI,iBAAiB,OAAO;AAC1B,wBAAMC,QAAO,KAAK,YAAY;AAC9B,wBAAMC,OAAM,OAAO,KAAK,IAAI,EACzB;AAAA,oBAAI,CAAC,MACJ,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM,MAAM,SAAS,CAAC,IAAI,IAAI;AAAA,kBACpD,EACC,OAAO,OAAO,EAAE,CAAC;AACpB,wBAAM,MAAM,KAAK;AAAA,oBACf,IAAI;AAAA,sBACF;AAAA,sBACA;AAAA,wBACE;AAAA,wBACA,6BAA6B,KAAK,UAAUD,MAAK,MAAM,CAAC,YAAYA,MAAK,KAAK,cAAc,KAAK,UAAU,KAAK,MAAM,MAAM,SAAS,QAAQ,CAAC,CAAC;AAAA,wBAC/IC,OAAM,IAAIA,IAAG,UAAU;AAAA,sBACzB;AAAA,sBACA,KAAK;AAAA,sBACLD;AAAA,sBACA;AAAA,wBACE,OAAO,KAAK,MAAM;AAAA,sBACpB;AAAA,oBACF;AAAA,kBACF;AACA,sBAAI,QAAQ,OAAW,QAAO;AAAA,gBAChC;AAAA,cACF,OAAO;AACL,qBAAK,MAAM,UAAU;AACrB,uBAAO,MAAM,SAAS;AACtB;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,KAAK,MAAM,YAAY,OAAO;AAChC,kBAAM,wBAAwB,KAAK;AAAA,cACjC;AAAA,cACA;AAAA,cACA;AAAA,YACF;AACA,gBAAI,0BAA0B,GAAG;AAE/B,oBAAM,kBACJ,KAAK,MAAM,cACX,KAAK,MAAM,eAAe,SAC1B,KAAK,MAAM,OAAO,WAAW,KAC7B,KAAK,MAAM,MAAM,WAAW;AAC9B,kBAAI,iBAAiB;AACnB,qBAAK,KAAK;AAAA,cAEZ,OAAO;AAEL,oBACE,KAAK,MAAM,YAAY,SACvB,KAAK,KAAK,SACP,KAAK,MAAM,oBAAoB,OAAO,IAAI,MAC3C,WACF;AACA,uBAAK,MAAM,UAAU;AACrB,uBAAK,aAAa;AAClB,uBAAK,cAAc;AACnB,yBAAO,wBAAwB;AAC/B;AAAA,gBACF;AAEA,oBACE,qBAAqB,QACrB,KAAK,MAAM,eAAe,SAC1B,KAAK,MAAM,OAAO,WAAW,KAC7B,KAAK,MAAM,MAAM,WAAW,GAC5B;AACA,uBAAK,KAAK;AACV,yBAAO,wBAAwB;AAC/B;AAAA,gBACF;AACA,qBAAK,KAAK,QAAQ,KAAK,MAAM,gBAAgB;AAC7C,sBAAM,WAAW,KAAK,UAAU;AAChC,oBAAI,aAAa,OAAW,QAAO;AACnC,qBAAK,KAAK,QACR,KAAK,MAAM,gBAAgB,MAAM;AACnC,sBAAM,YAAY,KAAK,WAAW,IAAI;AACtC,oBAAI,cAAc,OAAW,QAAO;AACpC,oBAAI,OAAO,MAAM,KAAK,KAAK,WAAW,IAAI;AACxC,uBAAK,MAAM,OAAO;AAClB,wBAAM;AACN;AAAA,gBACF;AAAA,cACF;AACA,mBAAK,MAAM,aAAa;AACxB,qBAAO,wBAAwB;AAC/B;AAAA,YACF;AACA,gBAAI,KAAK,MAAM,YAAY;AACzB;AAAA,YACF;AACA,gBACE,YAAY,SACX,qBAAqB,SACnB,KAAK,MAAM,OAAO,WAAW,KAC5B,KAAK,MAAM,MAAM,WAAW,IAChC;AACA,oBAAM,eAAe,KAAK,eAAe,SAAS,KAAK,KAAK,GAAG;AAC/D,kBAAI,iBAAiB,GAAG;AACtB,qBAAK,MAAM,aAAa;AACxB;AAAA,cACF;AAAA,YACF;AACA,kBAAM,kBAAkB,KAAK,cAAc,KAAK,KAAK,GAAG;AACxD,gBAAI,oBAAoB,GAAG;AACzB,mBAAK,KAAK,QAAQ,KAAK,MAAM,gBAAgB;AAC7C,oBAAM,WAAW,KAAK,UAAU;AAChC,kBAAI,aAAa,OAAW,QAAO;AACnC,qBAAO,kBAAkB;AACzB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,KAAK,MAAM,eAAe,OAAO;AACnC,cACE,oBAAoB,KACpB,KAAK,MAAM,gBAAgB,KAAK,MAAM,MAAM,SAAS,iBACrD;AACA,mBAAO,KAAK;AAAA,cACV,IAAI;AAAA,gBACF;AAAA,gBACA;AAAA,kBACE;AAAA,kBACA;AAAA,kBACA,MAAM,eAAe;AAAA,kBACrB,WAAW,KAAK,KAAK,KAAK;AAAA,gBAC5B;AAAA,gBACA,KAAK;AAAA,gBACL,KAAK,YAAY;AAAA,cACnB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,cAAM,UACJ,UAAU,SACV,KAAK,MAAM,YAAY,QACvB,KAAK,MAAM,MAAM,WAAW,KAC5B,CAAC,KAAK,iBAAiB,KAAK,GAAG;AAEjC,cAAM,UAAU,UAAU,SAAS,KAAK,MAAM,eAAe;AAC7D,YAAI,YAAY,QAAQ,YAAY,MAAM;AACxC,eAAK,MAAM,MAAM,OAAO,GAAG;AAAA,QAC7B,WAAW,UAAU,QAAQ,CAAC,KAAK,iBAAiB,KAAK,GAAG,GAAG;AAC7D,iBAAO,KAAK;AAAA,YACV,IAAI;AAAA,cACF;AAAA,cACA;AAAA,gBACE;AAAA,gBACA;AAAA,gBACA,WAAW,KAAK,KAAK,KAAK;AAAA,cAC5B;AAAA,cACA,KAAK;AAAA,cACL,KAAK,YAAY;AAAA,YACnB;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAI,YAAY,OAAO;AACrB,mBAAO,KAAK,iBAAiB,KAAK,GAAG,IAAI;AAAA,UAC3C;AACA;AAAA,QACF;AAAA,MACF;AACA,UAAI,QAAQ,MAAM;AAEhB,YAAI,KAAK,MAAM,YAAY,MAAM;AAC/B,gBAAM,MAAM,KAAK;AAAA,YACf,IAAI;AAAA,cACF;AAAA,cACA;AAAA,gBACE;AAAA,gBACA,yDAAyD,KAAK,KAAK,KAAK;AAAA,cAC1E;AAAA,cACA,KAAK;AAAA,cACL,KAAK,YAAY;AAAA,YACnB;AAAA,UACF;AACA,cAAI,QAAQ,OAAW,QAAO;AAAA,QAChC,OAAO;AAEL,cACE,KAAK,MAAM,eAAe,QAC1B,KAAK,MAAM,OAAO,WAAW,KAC7B,KAAK,MAAM,MAAM,WAAW,GAC5B;AACA,iBAAK,KAAK,QAAQ,KAAK,MAAM,gBAAgB;AAC7C,kBAAM,WAAW,KAAK,UAAU;AAChC,gBAAI,aAAa,OAAW,QAAO;AACnC,kBAAM,YAAY,KAAK,WAAW,IAAI;AACtC,gBAAI,cAAc,OAAW,QAAO;AAAA,UACtC,WAAW,KAAK,MAAM,oBAAoB,MAAM;AAC9C,iBAAK,KAAK;AAAA,UACZ,WAAW,KAAK,MAAM,eAAe,MAAM;AACzC,iBAAK,KAAK;AAAA,UACZ;AAAA,QACF;AAAA,MACF,OAAO;AACL,aAAK,MAAM,iBAAiB;AAC5B,aAAK,MAAM,cAAc,IAAI,MAAM,GAAG;AAAA,MACxC;AACA,UAAI,KAAK,MAAM,oBAAoB,MAAM;AACvC,aAAK,KAAK;AACV,aAAK,MAAM,kBAAkB;AAAA,MAC/B;AAAA,IACF;AAAA,IACA,YAAY,SAAU,MAAM;AAC1B,YAAM;AAAA,QACJ;AAAA,QACA;AAAA,QACA;AAAA,QACA,MAAAA;AAAA,QACA,MAAAE;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,MACF,IAAI,KAAK;AACT,YAAM,EAAE,SAAS,OAAO,IAAI,KAAK;AACjC,UAAI,YAAY,OAAO;AACrB,eAAO,KAAK,cAAc;AAAA,MAC5B;AAEA,YAAM,eAAe,OAAO;AAC5B,UAAI,YAAY,MAAM;AACpB,YAAI,mCAAmC,QAAQ,cAAc,MAAM,GAAG;AACpE,eAAK,cAAc;AACnB;AAAA,QACF;AACA,eAAO,KAAK,qBAAqB,MAAM;AAAA,MACzC;AACA,UAAI,YAAY,SAAS,KAAK,KAAK,YAAY,GAAG;AAChD,aAAK,MAAM,uBAAuB;AAAA,MACpC;AACA,UAAI,iBAAiB,KAAK,MAAM,sBAAsB;AACpD,cAAM,MACJ,YAAY,QACR,IAAI;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA,UAAU,KAAK,MAAM,oBAAoB;AAAA,YACzC,OAAO,YAAY,YAAY,KAAK,KAAK,KAAK;AAAA,UAChD;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY;AAAA,UACjB;AAAA,YACE;AAAA,UACF;AAAA,QACF,IACA,IAAI;AAAA,UACF;AAAA,UACA;AAAA,YACE;AAAA,YACA,qBAAqB,QAAQ,MAAM;AAAA;AAAA,YACnC,OAAO,YAAY,YAAY,KAAK,KAAK,KAAK;AAAA,UAChD;AAAA,UACA,KAAK;AAAA,UACL,KAAK,YAAY;AAAA,UACjB;AAAA,YACE;AAAA,UACF;AAAA,QACF;AACN,YACE,uBAAuB,QACtB,4BAA4B,QAC3B,eAAe,KAAK,MAAM,wBAC3B,4BAA4B,QAC3B,eAAe,KAAK,MAAM,sBAC5B;AACA,eAAK,KAAK;AACV,eAAK,MAAM,QAAQ;AAAA,QAErB,OAAO;AACL,gBAAM,WAAW,KAAK,QAAQ,GAAG;AACjC,cAAI,SAAU,QAAO;AAAA,QACvB;AAAA,MACF;AACA,UAAI,mCAAmC,QAAQ,cAAc,MAAM,GAAG;AACpE,aAAK,cAAc;AACnB;AAAA,MACF;AACA,UAAI,KAAK,MAAM,mBAAmB,MAAM;AACtC,aAAK,cAAc;AACnB,aAAK,MAAM,iBAAiB;AAC5B;AAAA,MACF;AACA,WAAK,KAAK;AACV,UAAIA,UAAS,KAAK,KAAK,KAAK,WAAWA,OAAM;AAC3C,cAAM,EAAE,QAAQ,IAAI,KAAK;AAEzB,YAAI,YAAY,OAAO;AACrB,gBAAM,MAAM,CAAC;AAEb,mBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,IAAI,GAAG,KAAK;AAC7C,gBAAI,QAAQ,CAAC,MAAM,UAAa,QAAQ,CAAC,EAAE,SAAU;AAErD,gBACE,0BAA0B,QAC1B,IAAI,QAAQ,CAAC,EAAE,IAAI,MAAM,QACzB;AACA,kBAAI,MAAM,QAAQ,IAAI,QAAQ,CAAC,EAAE,IAAI,CAAC,GAAG;AACvC,oBAAI,QAAQ,CAAC,EAAE,IAAI,IAAI,IAAI,QAAQ,CAAC,EAAE,IAAI,EAAE,OAAO,OAAO,CAAC,CAAC;AAAA,cAC9D,OAAO;AACL,oBAAI,QAAQ,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,QAAQ,CAAC,EAAE,IAAI,GAAG,OAAO,CAAC,CAAC;AAAA,cACzD;AAAA,YACF,OAAO;AACL,kBAAI,QAAQ,CAAC,EAAE,IAAI,IAAI,OAAO,CAAC;AAAA,YACjC;AAAA,UACF;AAEA,cAAI,QAAQ,QAAQF,UAAS,MAAM;AACjC,kBAAM,YAAY,OAAO;AAAA,cACvB,EAAE,QAAQ,IAAI;AAAA,cACd,QAAQ,OACJ,EAAE,KAAK,KAAK,MAAM,UAAU,SAAS,QAAQ,EAAE,IAC/C,CAAC;AAAA,cACLA,UAAS,OAAO,EAAE,MAAM,KAAK,aAAa,EAAE,IAAI,CAAC;AAAA,YACnD;AACA,kBAAM,MAAM,KAAK;AAAA,cACf,YAAY,SAAY,YAAY,CAAC,IAAI,OAAO,GAAG,SAAS;AAAA,cAC5D;AAAA,YACF;AACA,gBAAI,KAAK;AACP,qBAAO;AAAA,YACT;AAAA,UACF,OAAO;AACL,kBAAM,MAAM,KAAK;AAAA,cACf,YAAY,SAAY,MAAM,CAAC,IAAI,OAAO,GAAG,GAAG;AAAA,cAChD;AAAA,YACF;AACA,gBAAI,KAAK;AACP,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QAEF,OAAO;AACL,cAAI,QAAQ,QAAQA,UAAS,MAAM;AACjC,kBAAM,YAAY,OAAO;AAAA,cACvB,EAAE,OAAe;AAAA,cACjB,QAAQ,OACJ,EAAE,KAAK,KAAK,MAAM,UAAU,SAAS,QAAQ,EAAE,IAC/C,CAAC;AAAA,cACLA,UAAS,OAAO,EAAE,MAAM,KAAK,aAAa,EAAE,IAAI,CAAC;AAAA,YACnD;AACA,kBAAM,MAAM,KAAK;AAAA,cACf,YAAY,SAAY,YAAY,CAAC,OAAO,OAAO,GAAG,SAAS;AAAA,cAC/D;AAAA,YACF;AACA,gBAAI,KAAK;AACP,qBAAO;AAAA,YACT;AAAA,UACF,OAAO;AACL,kBAAM,MAAM,KAAK;AAAA,cACf,YAAY,SAAY,SAAS,CAAC,OAAO,OAAO,GAAG,MAAM;AAAA,cACzD;AAAA,YACF;AACA,gBAAI,KAAK;AACP,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,WAAK,cAAc;AAAA,IACrB;AAAA,IACA,sBAAsB,SAAU,QAAQ;AACtC,YAAM,EAAE,mBAAmB,IAAI,KAAK;AACpC,UAAI;AACF,cAAM,UACJ,uBAAuB,SACnB,SACA,mBAAmB,KAAK,MAAM,MAAM;AAC1C,YAAI,CAAC,MAAM,QAAQ,OAAO,GAAG;AAC3B,iBAAO,KAAK;AAAA,YACV,IAAI;AAAA,cACF;AAAA,cACA;AAAA,gBACE;AAAA,gBACA;AAAA,gBACA,OAAO,KAAK,UAAU,OAAO,CAAC;AAAA,cAChC;AAAA,cACA,KAAK;AAAA,cACL,KAAK,YAAY;AAAA,cACjB;AAAA,gBACE;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,cAAM,oBAAoB,wBAAwB,OAAO;AACzD,aAAK,MAAM,uBAAuB,kBAAkB;AACpD,aAAK,QAAQ,UAAU;AACvB,aAAK,cAAc;AACnB;AAAA,MACF,SAAS,KAAK;AACZ,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,eAAe,WAAY;AACzB,UAAI,KAAK,QAAQ,QAAQ,MAAM;AAC7B,aAAK,MAAM,UAAU,MAAM;AAAA,MAC7B;AACA,WAAK,MAAM,QAAQ;AACnB,WAAK,MAAM,SAAS,CAAC;AACrB,WAAK,MAAM,gBAAgB;AAAA,IAC7B;AAAA,IACA,WAAW,WAAY;AACrB,YAAM,EAAE,MAAM,UAAU,OAAO,gBAAgB,IAAI,KAAK;AACxD,YAAM,EAAE,SAAS,WAAW,IAAI,KAAK;AAErC,UAAI,YAAY,OAAO;AACrB,eAAO,KAAK,aAAa;AAAA,MAC3B;AACA,UAAI,QAAQ,KAAK,MAAM,MAAM,SAAS,QAAQ;AAC9C,UAAI,UAAU,QAAQ,eAAe,OAAO;AAC1C,gBAAQ,MAAM,UAAU;AAAA,MAC1B;AACA,UAAI,SAAS,MAAM;AACjB,cAAM,CAAC,KAAK,CAAC,IAAI,KAAK,OAAO,KAAK;AAClC,YAAI,QAAQ,OAAW,QAAO;AAC9B,gBAAQ;AAAA,MACV;AACA,WAAK,MAAM,OAAO,KAAK,KAAK;AAE5B,UAAI,oBAAoB,KAAK,OAAO,UAAU,UAAU;AACtD,aAAK,MAAM,iBAAiB,MAAM;AAAA,MACpC;AACA,WAAK,aAAa;AAAA,IACpB;AAAA,IACA,cAAc,WAAY;AACxB,WAAK,MAAM,MAAM,MAAM;AACvB,WAAK,MAAM,aAAa;AAAA,IAC1B;AAAA,IACA,QAAQ,SAAU,QAAQ,MAAM;AAC9B,YAAM,EAAE,UAAU,IAAI,KAAK;AAC3B,UAAI,cAAc,QAAW;AAC3B,cAAMA,QAAO,KAAK,aAAa;AAC/B,YAAI;AACF,mBAAS,UAAU,KAAK,MAAM,QAAQA,KAAI;AAAA,QAC5C,SAAS,KAAK;AACZ,iBAAO;AAAA,QACT;AACA,YAAI,WAAW,UAAa,WAAW,MAAM;AAC3C;AAAA,QACF;AAAA,MACF;AACA,WAAK,MAAM;AAAA,IACb;AAAA;AAAA,IAEA,QAAQ,SAAU,OAAO;AACvB,YAAM,EAAE,SAAS,mBAAmB,IAAI,KAAK;AAC7C,YAAM,YAAY,MAAM,QAAQ,OAAO;AAIvC,UACE,cAAc,QACd,sBACA,KAAK,QAAQ,QAAQ,UAAU,KAAK,MAAM,OAAO,QACjD;AACA,eAAO,CAAC,QAAW,MAAS;AAAA,MAC9B;AACA,UAAI,KAAK,MAAM,cAAc,MAAM;AACjC,YAAI;AACF,gBAAMA,QAAO,KAAK,YAAY;AAC9B,iBAAO,CAAC,QAAW,KAAK,MAAM,UAAU,KAAK,MAAM,OAAOA,KAAI,CAAC;AAAA,QACjE,SAAS,KAAK;AACZ,iBAAO,CAAC,GAAG;AAAA,QACb;AAAA,MACF;AACA,UAAI,KAAK,UAAU,KAAK,GAAG;AACzB,eAAO,CAAC,QAAW,WAAW,KAAK,CAAC;AAAA,MACtC,WAAW,KAAK,QAAQ,cAAc,OAAO;AAC3C,cAAMA,QAAO,KAAK,YAAY;AAC9B,eAAO,CAAC,QAAW,KAAK,QAAQ,UAAU,KAAK,MAAM,OAAOA,KAAI,CAAC;AAAA,MACnE;AACA,aAAO,CAAC,QAAW,KAAK;AAAA,IAC1B;AAAA;AAAA,IAEA,kBAAkB,SAAU,KAAK,KAAK;AACpC,YAAM,SAAS,CAACG,MAAKC,SAAQ;AAC3B,cAAM,EAAE,SAAS,IAAI,KAAK;AAC1B,cAAO,UAAS,IAAI,GAAG,IAAI,SAAS,QAAQ,KAAK;AAC/C,gBAAM,UAAU,SAAS,CAAC;AAC1B,mBAAS,IAAI,GAAG,IAAI,QAAQ,QAAQ,KAAK;AACvC,gBAAI,QAAQ,CAAC,MAAMD,KAAIC,OAAM,CAAC,EAAG,UAAS;AAAA,UAC5C;AACA,iBAAO,QAAQ;AAAA,QACjB;AACA,eAAO;AAAA,MACT;AACA,aAAO,OAAO,KAAK,GAAG;AAAA,IACxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAOA,WAAW,SAAU,OAAO;AAC1B,aAAO,QAAQ,WAAW,KAAK,IAAI,KAAK;AAAA,IAC1C;AAAA,IACA,gBAAgB,SAAU,WAAW,WAAW,WAAW,WAAW;AACpE,UAAI,UAAU,CAAC,MAAM,UAAW,QAAO;AACvC,YAAM,eAAe,UAAU;AAC/B,eAAS,IAAI,GAAG,IAAI,cAAc,KAAK;AACrC,YAAI,UAAU,CAAC,MAAM,UAAU,YAAY,CAAC,EAAG,QAAO;AAAA,MACxD;AACA,aAAO;AAAA,IACT;AAAA,IACA,eAAe,SAAU,KAAK,KAAK,KAAK;AACtC,YAAM,EAAE,WAAW,uBAAuB,IAAI,KAAK;AACnD,UACE,2BAA2B,QAC3B,KAAK,MAAM,OAAO,WAAW,KAAK,QAAQ,QAAQ,SAAS,GAC3D;AACA,eAAO;AAAA,MACT,WACE,2BAA2B,SAC3B,OAAO,2BAA2B,YAClC,KAAK,MAAM,OAAO,WAAW,yBAAyB,GACtD;AACA,eAAO;AAAA,MACT;AACA,YAAO,UAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,KAAK;AAChD,cAAM,MAAM,UAAU,CAAC;AACvB,YAAI,IAAI,CAAC,MAAM,KAAK;AAClB,mBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAI,IAAI,CAAC,MAAM,IAAI,MAAM,CAAC,EAAG,UAAS;AAAA,UACxC;AACA,iBAAO,IAAI;AAAA,QACb;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,IACA,qBAAqB,SAAU,KAAK,KAAK,KAAK;AAC5C,YAAM,EAAE,iBAAiB,IAAI,KAAK;AAClC,YAAM,wBAAwB,iBAAiB;AAC/C,YAAO,UAAS,IAAI,GAAG,IAAI,uBAAuB,KAAK;AACrD,cAAM,KAAK,iBAAiB,CAAC;AAC7B,cAAM,WAAW,GAAG;AACpB,YAAI,GAAG,CAAC,MAAM,KAAK;AACjB;AAAA,QACF;AACA,iBAAS,IAAI,GAAG,IAAI,UAAU,KAAK;AACjC,cAAI,GAAG,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG;AAC1B,qBAAS;AAAA,UACX;AAAA,QACF;AACA,eAAO,GAAG;AAAA,MACZ;AACA,aAAO;AAAA,IACT;AAAA,IACA,YAAY,SAAU,KAAK,KAAK,KAAK;AACnC,YAAM,EAAE,OAAO,IAAI,KAAK;AACxB,UAAI,WAAW,KAAM,QAAO;AAC5B,YAAM,IAAI,OAAO;AACjB,UAAI,OAAO,CAAC,MAAM,KAAK;AACrB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,OAAO,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG;AAC9B,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,IACA,WAAW,SAAU,KAAK,KAAK;AAC7B,YAAM,EAAE,MAAM,IAAI,KAAK;AACvB,UAAI,UAAU,KAAM,QAAO;AAC3B,YAAM,IAAI,MAAM;AAChB,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAI,MAAM,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG;AAC7B,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,IACA,+BAA+B,SAAU,KAAK,KAAK;AACjD,YAAM,EAAE,SAAS,IAAI,KAAK;AAI1B,YAAM,MAAM;AAAA;AAAA,QAEV,OAAO,KAAK,QAAQ,QAAQ;AAAA,QAC5B,OAAO,KAAK,MAAM,QAAQ;AAAA,QAC1B,OAAO,KAAK,MAAM,QAAQ;AAAA,MAC5B;AACA,WAAM,UAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACzC,cAAM,IAAI,IAAI,CAAC,EAAE;AACjB,iBAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAI,IAAI,CAAC,EAAE,CAAC,MAAM,IAAI,MAAM,CAAC,GAAG;AAC9B,qBAAS;AAAA,UACX;AAAA,QACF;AACA,aAAK,QAAQ,iBAAiB,KAAK,IAAI,CAAC,CAAC;AACzC,aAAK,MAAM,2BAA2B,IAAI,CAAC,EAAE;AAC7C,eAAO,IAAI,CAAC,EAAE;AAAA,MAChB;AACA,aAAO;AAAA,IACT;AAAA,IACA,SAAS,SAAU,KAAK;AACtB,YAAM,EAAE,UAAU,KAAK,wBAAwB,IAAI,KAAK;AACxD,YAAM,MAAM,OAAO,QAAQ,WAAW,IAAI,MAAM,GAAG,IAAI;AACvD,UAAI,yBAAyB;AAC3B,aAAK,MAAM,iBAAiB;AAC5B,YAAI,KAAK,QAAQ,YAAY,QAAW;AACtC,eAAK,QAAQ;AAAA,YACX;AAAA,YACA,MAAM,KAAK,MAAM,UAAU,SAAS,QAAQ,IAAI;AAAA,UAClD;AAAA,QACF;AAEA,eAAO;AAAA,MACT,OAAO;AACL,eAAO;AAAA,MACT;AAAA,IACF;AAAA,IACA,eAAe,WAAY;AACzB,aAAO;AAAA,QACL,GAAG,KAAK;AAAA,QACR,SAAS,KAAK,QAAQ;AAAA,MACxB;AAAA,IACF;AAAA,IACA,cAAc,WAAY;AACxB,YAAM,EAAE,SAAS,KAAK,SAAS,IAAI,KAAK;AACxC,aAAO;AAAA,QACL,GAAG,KAAK,cAAc;AAAA,QACtB,OAAO,KAAK,MAAM;AAAA,QAClB,QAAQ,YAAY;AAAA,QACpB,OAAO,KAAK,MAAM,OAAO;AAAA,QACzB,KAAK,MAAM,KAAK,MAAM,UAAU,SAAS,QAAQ,IAAI;AAAA,MACvD;AAAA,IACF;AAAA,IACA,aAAa,WAAY;AACvB,YAAM,EAAE,QAAQ,IAAI,KAAK;AACzB,YAAM,YAAY,MAAM,QAAQ,OAAO;AACvC,aAAO;AAAA,QACL,GAAG,KAAK,aAAa;AAAA,QACrB,QACE,cAAc,OACV,QAAQ,SAAS,KAAK,MAAM,OAAO,SACjC,QAAQ,KAAK,MAAM,OAAO,MAAM,EAAE,OAClC,OACF,KAAK,MAAM,OAAO;AAAA,QACxB,SAAS,KAAK,MAAM;AAAA,MACtB;AAAA,IACF;AAAA,EACF;AACF;AAEA,IAAM,QAAQ,SAAU,MAAM,OAAO,CAAC,GAAG;AACvC,MAAI,OAAO,SAAS,UAAU;AAC5B,WAAO,OAAO,KAAK,IAAI;AAAA,EACzB;AACA,QAAM,UAAU,QAAQ,KAAK,UAAU,CAAC,IAAI,CAAC;AAC7C,QAAM,SAAS,UAAU,IAAI;AAC7B,QAAM,OAAO,CAAC,WAAW;AACvB,QAAI,OAAO,QAAQ,YAAY,OAAW,SAAQ,KAAK,MAAM;AAAA,SACxD;AACH,cAAQ,OAAO,CAAC,CAAC,IAAI,OAAO,CAAC;AAAA,IAC/B;AAAA,EACF;AACA,QAAM,QAAQ,MAAM;AAAA,EAAC;AACrB,QAAM,OAAO,OAAO,MAAM,MAAM,OAAO,MAAM,KAAK;AAClD,MAAI,SAAS,OAAW,OAAM;AAC9B,QAAM,OAAO,OAAO,MAAM,QAAW,MAAM,MAAM,KAAK;AACtD,MAAI,SAAS,OAAW,OAAM;AAC9B,SAAO;AACT;", "names": ["fill", "toString", "compare", "read", "i", "write", "byteLength", "encoding", "info", "bom", "from", "buf", "pos"]}