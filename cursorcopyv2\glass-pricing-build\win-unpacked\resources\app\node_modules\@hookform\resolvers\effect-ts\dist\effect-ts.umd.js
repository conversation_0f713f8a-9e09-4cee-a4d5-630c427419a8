!function(e,r){"object"==typeof exports&&"undefined"!=typeof module?r(exports,require("@effect/schema/ArrayFormatter"),require("@effect/schema/ParseResult"),require("@hookform/resolvers"),require("effect/Effect")):"function"==typeof define&&define.amd?define(["exports","@effect/schema/ArrayFormatter","@effect/schema/ParseResult","@hookform/resolvers","effect/Effect"],r):r((e||self).hookformResolversEffectTs={},e.EffectSchemaArrayFormatter,e.EffectSchemaArrayFormatter,e.hookformResolvers,e.Effect)}(this,function(e,r,t,o,n){function f(e){if(e&&e.__esModule)return e;var r=Object.create(null);return e&&Object.keys(e).forEach(function(t){if("default"!==t){var o=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(r,t,o.get?o:{enumerable:!0,get:function(){return e[t]}})}}),r.default=e,r}var c=/*#__PURE__*/f(n);e.effectTsResolver=function(e,n){return void 0===n&&(n={errors:"all",onExcessProperty:"ignore"}),function(f,s,u){return t.decodeUnknown(e,n)(f).pipe(c.catchAll(function(e){return c.flip(r.formatIssue(e))}),c.mapError(function(e){var r=e.reduce(function(e,r){return e[r.path.join(".")]={message:r.message,type:r._tag},e},{});return o.toNestErrors(r,u)}),c.tap(function(){return c.sync(function(){return u.shouldUseNativeValidation&&o.validateFieldsNatively({},u)})}),c.match({onFailure:function(e){return{errors:e,values:{}}},onSuccess:function(e){return{errors:{},values:e}}}),c.runPromise)}}});
//# sourceMappingURL=effect-ts.umd.js.map
