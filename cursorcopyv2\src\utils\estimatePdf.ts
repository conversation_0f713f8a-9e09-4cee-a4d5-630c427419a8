import { jsPDF } from 'jspdf';
import autoTable from 'jspdf-autotable';
import { EstimateImage } from '@/services/imageService';
import { decimalToFraction } from './measurementUtils';

/**
 * Generates a PDF for an estimate
 * @param estimate The estimate to generate a PDF for
 * @param images Optional array of images to include in the PDF
 * @returns The generated PDF document
 */
export const generateEstimatePDF = (estimate: any, images?: EstimateImage[]): jsPDF => {
  try {
    // Create a new PDF document
    const doc = new jsPDF();

    // Add title
    doc.setFontSize(20);
    doc.text(estimate.name, 14, 22);

    // Add date
    doc.setFontSize(10);
    doc.text(`Date: ${new Date(estimate.date).toLocaleDateString()}`, 14, 30);

    // Add company info if available
    const customerInfo = estimate.customerInfo || {};
    let companyInfoY = 40; // Starting Y position for company info

    // Check if any company information is provided
    const hasCompanyInfo = customerInfo.companyName || customerInfo.companyAddress ||
                          customerInfo.companyCity || customerInfo.companyState ||
                          customerInfo.companyZip || customerInfo.contactName ||
                          customerInfo.contactPhone;

    if (hasCompanyInfo) {
      // Company information
      if (customerInfo.companyName) {
        doc.text(customerInfo.companyName, 14, companyInfoY);
        companyInfoY += 5;
      }

      if (customerInfo.companyAddress) {
        doc.text(customerInfo.companyAddress, 14, companyInfoY);
        companyInfoY += 5;
      }

      // Format city, state, zip
      const cityStateZip = [
        customerInfo.companyCity,
        customerInfo.companyState,
        customerInfo.companyZip
      ].filter(Boolean).join(', ');

      if (cityStateZip) {
        doc.text(cityStateZip, 14, companyInfoY);
        companyInfoY += 5;
      }

      // Contact information
      if (customerInfo.contactPhone) {
        doc.text(customerInfo.contactPhone, 14, companyInfoY);
        companyInfoY += 5;
      }
    }

    // Project information (if different from company and project info exists)
    const hasProjectInfo = customerInfo.projectName || customerInfo.projectAddress ||
                          customerInfo.projectCity || customerInfo.projectState ||
                          customerInfo.projectZip;

    if (customerInfo.useProjectAddress === false && hasProjectInfo) {
      let projectInfoY = 40; // Starting Y position for project info

      if (customerInfo.projectName) {
        doc.text('Project: ' + customerInfo.projectName, 120, projectInfoY);
        projectInfoY += 5;
      }

      if (customerInfo.projectAddress) {
        doc.text(customerInfo.projectAddress, 120, projectInfoY);
        projectInfoY += 5;

        // Format project city, state, zip
        const projectCityStateZip = [
          customerInfo.projectCity,
          customerInfo.projectState,
          customerInfo.projectZip
        ].filter(Boolean).join(', ');

        if (projectCityStateZip) {
          doc.text(projectCityStateZip, 120, projectInfoY);
        }
      }
    }

    // Prepare table data
    const tableColumn = ['Glass Type', 'Thickness', 'Dimensions', 'Qty', 'Sq Ft', 'Weight', 'Price'];
    const tableRows: any[] = [];

    // Calculate totals
    let totalSqFt = 0;
    let totalWeight = 0;
    let totalPrice = 0;

    // Add items to table
    estimate.items.forEach((item: any) => {
      const width = item.adjustedWidth || item.width;
      const height = item.adjustedHeight || item.height;

      // Get the roundUpGlassSize setting
      const roundUpGlassSize = estimate.settings?.roundUpGlassSize || false;

      // Calculate dimensions based on roundUpGlassSize setting
      let pricingWidth = width;
      let pricingHeight = height;

      if (roundUpGlassSize) {
        // Round up to the next even number for pricing
        pricingWidth = Math.ceil(width / 2) * 2;
        pricingHeight = Math.ceil(height / 2) * 2;
      }

      // Calculate area for display (using original dimensions)
      const sqFt = (width * height) / 144; // Convert square inches to square feet
      const totalItemSqFt = sqFt * item.quantity;

      // Calculate area for pricing (using rounded dimensions if enabled)
      const pricingSqFt = (pricingWidth * pricingHeight) / 144;

      // Calculate weight (using pricing area for consistency)
      const weightPerPiece = pricingSqFt * item.glassType.lbsPerSqFt;
      const totalItemWeight = weightPerPiece * item.quantity;

      // Calculate base price
      let pricePerSqFt = item.glassType.price || item.glassType.costPerSqFt || 0;

      // Add cost for insulated glass options if applicable
      if (item.hasArgonGas) {
        // Add $2.00 per square foot for argon gas
        pricePerSqFt += 2.00;
      }

      if (item.hasAluminumSpacer) {
        // Add $0.75 per square foot for aluminum spacer
        pricePerSqFt += 0.75;
      }

      // Calculate final price using pricing area
      const pricePerPiece = pricingSqFt * pricePerSqFt;
      const totalItemPrice = pricePerPiece * item.quantity;

      // Update totals
      totalSqFt += totalItemSqFt;
      totalWeight += totalItemWeight;
      totalPrice += totalItemPrice;

      // Format dimensions using fractional format
      const dimensions = `${decimalToFraction(width)}" × ${decimalToFraction(height)}"${item.measurementType === 'DLO' ? ' (GS)' : ''}`;

      // Prepare glass type name with notes
      let glassTypeName = item.glassType.name;

      // Add notes if available
      if (item.notes && item.notes.length > 0) {
        glassTypeName += '\n' + item.notes.join('\n');
      }

      // Add row to table
      tableRows.push([
        glassTypeName,
        item.glassType.thicknessStr || item.glassType.thickness,
        dimensions,
        item.quantity,
        totalItemSqFt.toFixed(2),
        totalItemWeight.toFixed(2) + ' lbs',
        '$' + totalItemPrice.toFixed(2)
      ]);
    });

    // Determine the starting Y position for the table based on whether company info was displayed
    // If no company info was displayed, start the table closer to the top
    const tableStartY = hasCompanyInfo || hasProjectInfo ? 70 : 40;

    // Add the table to the PDF
    autoTable(doc, {
      head: [tableColumn],
      body: tableRows,
      startY: tableStartY,
      theme: 'grid',
      styles: { fontSize: 8 },
      headStyles: { fillColor: [66, 135, 245] },
      columnStyles: {
        0: { cellWidth: 'auto', cellPadding: 2 }, // Glass Type column with notes
      },
      didParseCell: function(data) {
        // For the glass type column, set lineHeight to accommodate notes
        if (data.column.index === 0 && data.cell.text && data.cell.text.length > 1) {
          data.cell.styles.lineColor = [200, 200, 200];
          data.cell.styles.lineWidth = 0.1;
        }
      }
    });

    // Add summary
    const finalY = (doc as any).lastAutoTable.finalY + 10;
    doc.text(`Total Square Footage: ${totalSqFt.toFixed(2)} sq ft`, 14, finalY);
    doc.text(`Total Weight: ${totalWeight.toFixed(2)} lbs`, 14, finalY + 6);
    doc.text(`Subtotal: $${totalPrice.toFixed(2)}`, 14, finalY + 12);

    // Get settings from the estimate or use defaults
    const settings = estimate.settings || {};

    // Calculate shipping (default 15% of subtotal)
    const shippingRate = settings.shippingRate || 15;
    const shippingAmount = totalPrice * (shippingRate / 100);
    doc.text(`Shipping (${shippingRate}%): $${shippingAmount.toFixed(2)}`, 14, finalY + 18);

    // Calculate surcharge if applicable
    const surchargeRate = settings.surchargeRate || 0;
    let surchargeAmount = 0;
    let currentY = finalY + 24;

    if (surchargeRate > 0) {
      surchargeAmount = totalPrice * (surchargeRate / 100);
      doc.text(`Surcharge (${surchargeRate}%): $${surchargeAmount.toFixed(2)}`, 14, currentY);
      currentY += 6;
    }

    // Calculate tax (default 8.25% of subtotal + shipping + surcharge)
    const taxRate = settings.taxRate || 8.25;
    const taxAmount = (totalPrice + shippingAmount + surchargeAmount) * (taxRate / 100);
    doc.text(`Tax (${taxRate}%): $${taxAmount.toFixed(2)}`, 14, currentY);
    currentY += 6;

    // Calculate total
    const total = totalPrice + shippingAmount + surchargeAmount + taxAmount;
    doc.setFontSize(12);
    doc.text(`Total: $${total.toFixed(2)}`, 14, currentY + 2);

    // Add images if available
    const estimateImages = images || estimate.images;
    if (estimateImages && estimateImages.length > 0) {
      console.log(`Adding ${estimateImages.length} images to PDF`);

      // Start a new page for images
      doc.addPage();

      // Add title for images
      doc.setFontSize(16);
      doc.text('Attached Images', 14, 20);

      // Calculate how many images to show per row and their size
      const imagesPerRow = 2;
      const imageWidth = 80; // mm
      const imageHeight = 60; // mm
      const margin = 14; // mm
      const spacing = 10; // mm between images

      // Add each image
      let row = 0;
      let col = 0;

      // Log the images for debugging
      console.log('Images to add to PDF:', estimateImages.map(img => ({
        id: img.id,
        fileName: img.fileName,
        hasDataUrl: !!img.dataUrl,
        dataUrlLength: img.dataUrl ? img.dataUrl.length : 0
      })));

      for (let i = 0; i < Math.min(estimateImages.length, 6); i++) { // Limit to 6 images
        const image = estimateImages[i];

        // Calculate position
        const x = margin + col * (imageWidth + spacing);
        const y = 30 + row * (imageHeight + spacing + 10); // Extra 10mm for caption

        try {
          // Add the image
          doc.addImage(
            image.dataUrl,
            'JPEG',
            x,
            y,
            imageWidth,
            imageHeight
          );

          // Add caption (filename and description)
          doc.setFontSize(8);
          doc.text(image.fileName, x, y + imageHeight + 5, { maxWidth: imageWidth });

          if (image.description) {
            doc.text(image.description, x, y + imageHeight + 10, { maxWidth: imageWidth });
          }

          // Update column and row
          col++;
          if (col >= imagesPerRow) {
            col = 0;
            row++;

            // Add a new page if we need more rows
            if (row >= 2 && i < estimateImages.length - 1) {
              doc.addPage();
              row = 0;
              doc.setFontSize(16);
              doc.text('Attached Images (continued)', 14, 20);
            }
          }
        } catch (error) {
          console.error('Error adding image to PDF:', error);
          // Continue with other images
        }
      }

      // If there are more images than we can show, add a note
      if (estimateImages.length > 6) {
        const noteY = 30 + row * (imageHeight + spacing + 10) + (col > 0 ? 0 : imageHeight + spacing + 10);
        doc.setFontSize(10);
        doc.text(`Note: ${estimateImages.length - 6} additional images are not shown in this PDF.`, 14, noteY);
      }
    }

    return doc;
  } catch (error) {
    console.error('Error generating PDF:', error);
    throw error;
  }
};
