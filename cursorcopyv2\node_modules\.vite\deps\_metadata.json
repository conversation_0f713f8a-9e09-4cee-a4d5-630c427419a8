{"hash": "a7dc4b67", "configHash": "9cc696f6", "lockfileHash": "c71dba0d", "browserHash": "ad94b4cc", "optimized": {"react/jsx-dev-runtime": {"src": "../../react/jsx-dev-runtime.js", "file": "react_jsx-dev-runtime.js", "fileHash": "f0aaedc2", "needsInterop": true}, "@radix-ui/react-alert-dialog": {"src": "../../@radix-ui/react-alert-dialog/dist/index.mjs", "file": "@radix-ui_react-alert-dialog.js", "fileHash": "d601ccda", "needsInterop": false}, "@radix-ui/react-dialog": {"src": "../../@radix-ui/react-dialog/dist/index.mjs", "file": "@radix-ui_react-dialog.js", "fileHash": "1d2ab7e6", "needsInterop": false}, "@radix-ui/react-label": {"src": "../../@radix-ui/react-label/dist/index.mjs", "file": "@radix-ui_react-label.js", "fileHash": "bfef9b3f", "needsInterop": false}, "@radix-ui/react-progress": {"src": "../../@radix-ui/react-progress/dist/index.mjs", "file": "@radix-ui_react-progress.js", "fileHash": "7d2575b5", "needsInterop": false}, "@radix-ui/react-radio-group": {"src": "../../@radix-ui/react-radio-group/dist/index.mjs", "file": "@radix-ui_react-radio-group.js", "fileHash": "81f772bb", "needsInterop": false}, "@radix-ui/react-select": {"src": "../../@radix-ui/react-select/dist/index.mjs", "file": "@radix-ui_react-select.js", "fileHash": "19e5b396", "needsInterop": false}, "@radix-ui/react-slot": {"src": "../../@radix-ui/react-slot/dist/index.mjs", "file": "@radix-ui_react-slot.js", "fileHash": "717754a7", "needsInterop": false}, "@radix-ui/react-switch": {"src": "../../@radix-ui/react-switch/dist/index.mjs", "file": "@radix-ui_react-switch.js", "fileHash": "771a9362", "needsInterop": false}, "@radix-ui/react-tabs": {"src": "../../@radix-ui/react-tabs/dist/index.mjs", "file": "@radix-ui_react-tabs.js", "fileHash": "8a80190a", "needsInterop": false}, "@radix-ui/react-toggle": {"src": "../../@radix-ui/react-toggle/dist/index.mjs", "file": "@radix-ui_react-toggle.js", "fileHash": "1f4c6fbf", "needsInterop": false}, "@radix-ui/react-toggle-group": {"src": "../../@radix-ui/react-toggle-group/dist/index.mjs", "file": "@radix-ui_react-toggle-group.js", "fileHash": "f583743a", "needsInterop": false}, "@radix-ui/react-tooltip": {"src": "../../@radix-ui/react-tooltip/dist/index.mjs", "file": "@radix-ui_react-tooltip.js", "fileHash": "0c0f4033", "needsInterop": false}, "@tanstack/react-query": {"src": "../../@tanstack/react-query/build/modern/index.js", "file": "@tanstack_react-query.js", "fileHash": "33ca5c20", "needsInterop": false}, "class-variance-authority": {"src": "../../class-variance-authority/dist/index.mjs", "file": "class-variance-authority.js", "fileHash": "0757010c", "needsInterop": false}, "clsx": {"src": "../../clsx/dist/clsx.mjs", "file": "clsx.js", "fileHash": "5d414744", "needsInterop": false}, "csv-parse/browser/esm/sync": {"src": "../../csv-parse/dist/esm/sync.js", "file": "csv-parse_browser_esm_sync.js", "fileHash": "e02e0bb8", "needsInterop": false}, "framer-motion": {"src": "../../framer-motion/dist/es/index.mjs", "file": "framer-motion.js", "fileHash": "f06c6036", "needsInterop": false}, "idb": {"src": "../../idb/build/index.js", "file": "idb.js", "fileHash": "d55937ee", "needsInterop": false}, "jspdf": {"src": "../../jspdf/dist/jspdf.es.min.js", "file": "jspdf.js", "fileHash": "73b32cff", "needsInterop": false}, "jspdf-autotable": {"src": "../../jspdf-autotable/dist/jspdf.plugin.autotable.mjs", "file": "jspdf-autotable.js", "fileHash": "f74e7b00", "needsInterop": false}, "lucide-react": {"src": "../../lucide-react/dist/esm/lucide-react.js", "file": "lucide-react.js", "fileHash": "a6d813e9", "needsInterop": false}, "next-themes": {"src": "../../next-themes/dist/index.mjs", "file": "next-themes.js", "fileHash": "c3551e92", "needsInterop": false}, "react": {"src": "../../react/index.js", "file": "react.js", "fileHash": "d867f213", "needsInterop": true}, "react-dom/client": {"src": "../../react-dom/client.js", "file": "react-dom_client.js", "fileHash": "32e15705", "needsInterop": true}, "react-router-dom": {"src": "../../react-router-dom/dist/index.js", "file": "react-router-dom.js", "fileHash": "2e3814ec", "needsInterop": false}, "react/jsx-runtime": {"src": "../../react/jsx-runtime.js", "file": "react_jsx-runtime.js", "fileHash": "f553018f", "needsInterop": true}, "sonner": {"src": "../../sonner/dist/index.mjs", "file": "sonner.js", "fileHash": "717bdd76", "needsInterop": false}, "tailwind-merge": {"src": "../../tailwind-merge/dist/bundle-mjs.mjs", "file": "tailwind-merge.js", "fileHash": "f82da192", "needsInterop": false}, "uuid": {"src": "../../uuid/dist/esm-browser/index.js", "file": "uuid.js", "fileHash": "a3c5a45e", "needsInterop": false}}, "chunks": {"index.es-KRL5PXYW": {"file": "index__es-KRL5PXYW.js"}, "chunk-D7ZASVPN": {"file": "chunk-D7ZASVPN.js"}, "html2canvas.esm-DQ6QHELO": {"file": "html2canvas__esm-DQ6QHELO.js"}, "purify.es-65KTZII5": {"file": "purify__es-65KTZII5.js"}, "chunk-EUXQ2SKF": {"file": "chunk-EUXQ2SKF.js"}, "chunk-U7P2NEEE": {"file": "chunk-U7P2NEEE.js"}, "chunk-B7ZEG2Y2": {"file": "chunk-B7ZEG2Y2.js"}, "chunk-26VWTK75": {"file": "chunk-26VWTK75.js"}, "chunk-EU6SEOIZ": {"file": "chunk-EU6SEOIZ.js"}, "chunk-2E4UK23M": {"file": "chunk-2E4UK23M.js"}, "chunk-DDW565K2": {"file": "chunk-DDW565K2.js"}, "chunk-2TWK77KC": {"file": "chunk-2TWK77KC.js"}, "chunk-PHY5VDMH": {"file": "chunk-PHY5VDMH.js"}, "chunk-XKHC42QG": {"file": "chunk-XKHC42QG.js"}, "chunk-NTSFJFQR": {"file": "chunk-NTSFJFQR.js"}, "chunk-FWUY5KVM": {"file": "chunk-FWUY5KVM.js"}, "chunk-C4HAUEYT": {"file": "chunk-C4HAUEYT.js"}, "chunk-GV6UPHID": {"file": "chunk-GV6UPHID.js"}, "chunk-H3Y66BRL": {"file": "chunk-H3Y66BRL.js"}, "chunk-AT2LYYK3": {"file": "chunk-AT2LYYK3.js"}, "chunk-WPQCFWW4": {"file": "chunk-WPQCFWW4.js"}, "chunk-TQG5UYZM": {"file": "chunk-TQG5UYZM.js"}, "chunk-S77I6LSE": {"file": "chunk-S77I6LSE.js"}, "chunk-3TFVT2CW": {"file": "chunk-3TFVT2CW.js"}, "chunk-4MBMRILA": {"file": "chunk-4MBMRILA.js"}}}