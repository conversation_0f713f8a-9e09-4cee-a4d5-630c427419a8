const { app, BrowserWindow, ipcMain, dialog } = require('electron');
const path = require('path');
const fs = require('fs');
const url = require('url');

let mainWindow;
let appDataPath;

function createWindow() {
  // Create the browser window.
  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.cjs'),
    },
    icon: path.join(__dirname, '../dist/images/icon.png'),
    show: false, // Don't show until ready-to-show
  });

  // Set up the app data directory for storing estimates and settings
  appDataPath = path.join(app.getPath('userData'), 'GlassScribeData');
  console.log('App data path:', appDataPath);
  if (!fs.existsSync(appDataPath)) {
    fs.mkdirSync(appDataPath, { recursive: true });
    console.log('Created app data directory');
  } else {
    console.log('App data directory already exists');
  }

  // In production, load the bundled app
  if (app.isPackaged) {
    mainWindow.loadFile(path.join(__dirname, '../dist/index.html'));
  } else {
    // In development, load from the dev server
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window close
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// This method will be called when Electron has finished initialization
app.whenReady().then(() => {
  createWindow();

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

// Quit when all windows are closed, except on macOS.
app.on('window-all-closed', function () {
  if (process.platform !== 'darwin') app.quit();
});

// IPC handlers for file operations
ipcMain.handle('get-app-data-path', () => {
  return appDataPath;
});

// Load data from file
ipcMain.handle('load-data', async (event, filename) => {
  try {
    const filePath = path.join(appDataPath, filename);
    console.log(`Attempting to load data from: ${filePath}`);
    if (fs.existsSync(filePath)) {
      console.log(`File exists: ${filePath}`);
      const data = fs.readFileSync(filePath, 'utf8');
      console.log(`Data loaded successfully from: ${filePath}`);
      return { success: true, data: JSON.parse(data) };
    }
    console.log(`File does not exist: ${filePath}`);
    return { success: false, error: 'File does not exist' };
  } catch (error) {
    console.error(`Error loading ${filename}:`, error);
    return { success: false, error: error.message };
  }
});

// Save data to file
ipcMain.handle('save-data', async (event, filename, data) => {
  try {
    const filePath = path.join(appDataPath, filename);
    console.log(`Attempting to save data to: ${filePath}`);
    console.log('Data to save:', JSON.stringify(data));
    fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8');
    console.log(`Data saved successfully to: ${filePath}`);

    // Verify the file was created
    if (fs.existsSync(filePath)) {
      console.log(`Verified file exists after save: ${filePath}`);
    } else {
      console.error(`File does not exist after save attempt: ${filePath}`);
    }

    return { success: true };
  } catch (error) {
    console.error(`Error saving ${filename}:`, error);
    return { success: false, error: error.message };
  }
});

// Save PDF file
ipcMain.handle('save-pdf', async (event, { pdfData, defaultFilename }) => {
  try {
    const { filePath } = await dialog.showSaveDialog({
      defaultPath: path.join(app.getPath('documents'), defaultFilename),
      filters: [{ name: 'PDF Files', extensions: ['pdf'] }],
    });

    if (filePath) {
      // Convert base64 data to buffer
      const pdfBuffer = Buffer.from(pdfData.split(',')[1], 'base64');
      fs.writeFileSync(filePath, pdfBuffer);
      return { success: true, filePath };
    }
    return { success: false, error: 'Save cancelled' };
  } catch (error) {
    console.error('Error saving PDF:', error);
    return { success: false, error: error.message };
  }
});
