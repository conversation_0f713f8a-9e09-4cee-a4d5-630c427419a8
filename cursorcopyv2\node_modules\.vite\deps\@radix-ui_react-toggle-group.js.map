{"version": 3, "sources": ["../../@radix-ui/react-toggle-group/src/ToggleGroup.tsx", "../../@radix-ui/react-toggle-group/node_modules/@radix-ui/react-context/src/createContext.tsx"], "sourcesContent": ["import React from 'react';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport * as RovingFocusGroup from '@radix-ui/react-roving-focus';\nimport { createRovingFocusGroupScope } from '@radix-ui/react-roving-focus';\nimport { Toggle } from '@radix-ui/react-toggle';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\n/* -------------------------------------------------------------------------------------------------\n * ToggleGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst TOGGLE_GROUP_NAME = 'ToggleGroup';\n\ntype ScopedProps<P> = P & { __scopeToggleGroup?: Scope };\nconst [createToggleGroupContext, createToggleGroupScope] = createContextScope(TOGGLE_GROUP_NAME, [\n  createRovingFocusGroupScope,\n]);\nconst useRovingFocusGroupScope = createRovingFocusGroupScope();\n\ntype ToggleGroupElement = ToggleGroupImplSingleElement | ToggleGroupImplMultipleElement;\ninterface ToggleGroupSingleProps extends ToggleGroupImplSingleProps {\n  type: 'single';\n}\ninterface ToggleGroupMultipleProps extends ToggleGroupImplMultipleProps {\n  type: 'multiple';\n}\n\nconst ToggleGroup = React.forwardRef<\n  ToggleGroupElement,\n  ToggleGroupSingleProps | ToggleGroupMultipleProps\n>((props, forwardedRef) => {\n  const { type, ...toggleGroupProps } = props;\n\n  if (type === 'single') {\n    const singleProps = toggleGroupProps as ToggleGroupImplSingleProps;\n    return <ToggleGroupImplSingle {...singleProps} ref={forwardedRef} />;\n  }\n\n  if (type === 'multiple') {\n    const multipleProps = toggleGroupProps as ToggleGroupImplMultipleProps;\n    return <ToggleGroupImplMultiple {...multipleProps} ref={forwardedRef} />;\n  }\n\n  throw new Error(`Missing prop \\`type\\` expected on \\`${TOGGLE_GROUP_NAME}\\``);\n});\n\nToggleGroup.displayName = TOGGLE_GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ToggleGroupValueContextValue = {\n  type: 'single' | 'multiple';\n  value: string[];\n  onItemActivate(value: string): void;\n  onItemDeactivate(value: string): void;\n};\n\nconst [ToggleGroupValueProvider, useToggleGroupValueContext] =\n  createToggleGroupContext<ToggleGroupValueContextValue>(TOGGLE_GROUP_NAME);\n\ntype ToggleGroupImplSingleElement = ToggleGroupImplElement;\ninterface ToggleGroupImplSingleProps extends ToggleGroupImplProps {\n  /**\n   * The controlled stateful value of the item that is pressed.\n   */\n  value?: string;\n  /**\n   * The value of the item that is pressed when initially rendered. Use\n   * `defaultValue` if you do not need to control the state of a toggle group.\n   */\n  defaultValue?: string;\n  /**\n   * The callback that fires when the value of the toggle group changes.\n   */\n  onValueChange?(value: string): void;\n}\n\nconst ToggleGroupImplSingle = React.forwardRef<\n  ToggleGroupImplSingleElement,\n  ToggleGroupImplSingleProps\n>((props: ScopedProps<ToggleGroupImplSingleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...toggleGroupSingleProps\n  } = props;\n\n  const [value, setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange,\n  });\n\n  return (\n    <ToggleGroupValueProvider\n      scope={props.__scopeToggleGroup}\n      type=\"single\"\n      value={value ? [value] : []}\n      onItemActivate={setValue}\n      onItemDeactivate={React.useCallback(() => setValue(''), [setValue])}\n    >\n      <ToggleGroupImpl {...toggleGroupSingleProps} ref={forwardedRef} />\n    </ToggleGroupValueProvider>\n  );\n});\n\ntype ToggleGroupImplMultipleElement = ToggleGroupImplElement;\ninterface ToggleGroupImplMultipleProps extends ToggleGroupImplProps {\n  /**\n   * The controlled stateful value of the items that are pressed.\n   */\n  value?: string[];\n  /**\n   * The value of the items that are pressed when initially rendered. Use\n   * `defaultValue` if you do not need to control the state of a toggle group.\n   */\n  defaultValue?: string[];\n  /**\n   * The callback that fires when the state of the toggle group changes.\n   */\n  onValueChange?(value: string[]): void;\n}\n\nconst ToggleGroupImplMultiple = React.forwardRef<\n  ToggleGroupImplMultipleElement,\n  ToggleGroupImplMultipleProps\n>((props: ScopedProps<ToggleGroupImplMultipleProps>, forwardedRef) => {\n  const {\n    value: valueProp,\n    defaultValue,\n    onValueChange = () => {},\n    ...toggleGroupMultipleProps\n  } = props;\n\n  const [value = [], setValue] = useControllableState({\n    prop: valueProp,\n    defaultProp: defaultValue,\n    onChange: onValueChange,\n  });\n\n  const handleButtonActivate = React.useCallback(\n    (itemValue: string) => setValue((prevValue = []) => [...prevValue, itemValue]),\n    [setValue]\n  );\n\n  const handleButtonDeactivate = React.useCallback(\n    (itemValue: string) =>\n      setValue((prevValue = []) => prevValue.filter((value) => value !== itemValue)),\n    [setValue]\n  );\n\n  return (\n    <ToggleGroupValueProvider\n      scope={props.__scopeToggleGroup}\n      type=\"multiple\"\n      value={value}\n      onItemActivate={handleButtonActivate}\n      onItemDeactivate={handleButtonDeactivate}\n    >\n      <ToggleGroupImpl {...toggleGroupMultipleProps} ref={forwardedRef} />\n    </ToggleGroupValueProvider>\n  );\n});\n\nToggleGroup.displayName = TOGGLE_GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ToggleGroupContextValue = { rovingFocus: boolean; disabled: boolean };\n\nconst [ToggleGroupContext, useToggleGroupContext] =\n  createToggleGroupContext<ToggleGroupContextValue>(TOGGLE_GROUP_NAME);\n\ntype RovingFocusGroupProps = React.ComponentPropsWithoutRef<typeof RovingFocusGroup.Root>;\ntype ToggleGroupImplElement = React.ElementRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface ToggleGroupImplProps extends PrimitiveDivProps {\n  /**\n   * Whether the group is disabled from user interaction.\n   * @defaultValue false\n   */\n  disabled?: boolean;\n  /**\n   * Whether the group should maintain roving focus of its buttons.\n   * @defaultValue true\n   */\n  rovingFocus?: boolean;\n  loop?: RovingFocusGroupProps['loop'];\n  orientation?: RovingFocusGroupProps['orientation'];\n  dir?: RovingFocusGroupProps['dir'];\n}\n\nconst ToggleGroupImpl = React.forwardRef<ToggleGroupImplElement, ToggleGroupImplProps>(\n  (props: ScopedProps<ToggleGroupImplProps>, forwardedRef) => {\n    const {\n      __scopeToggleGroup,\n      disabled = false,\n      rovingFocus = true,\n      orientation,\n      dir,\n      loop = true,\n      ...toggleGroupProps\n    } = props;\n    const rovingFocusGroupScope = useRovingFocusGroupScope(__scopeToggleGroup);\n    const direction = useDirection(dir);\n    const commonProps = { role: 'group', dir: direction, ...toggleGroupProps };\n    return (\n      <ToggleGroupContext scope={__scopeToggleGroup} rovingFocus={rovingFocus} disabled={disabled}>\n        {rovingFocus ? (\n          <RovingFocusGroup.Root\n            asChild\n            {...rovingFocusGroupScope}\n            orientation={orientation}\n            dir={direction}\n            loop={loop}\n          >\n            <Primitive.div {...commonProps} ref={forwardedRef} />\n          </RovingFocusGroup.Root>\n        ) : (\n          <Primitive.div {...commonProps} ref={forwardedRef} />\n        )}\n      </ToggleGroupContext>\n    );\n  }\n);\n\n/* -------------------------------------------------------------------------------------------------\n * ToggleGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'ToggleGroupItem';\n\ntype ToggleGroupItemElement = ToggleGroupItemImplElement;\ninterface ToggleGroupItemProps extends Omit<ToggleGroupItemImplProps, 'pressed'> {}\n\nconst ToggleGroupItem = React.forwardRef<ToggleGroupItemElement, ToggleGroupItemProps>(\n  (props: ScopedProps<ToggleGroupItemProps>, forwardedRef) => {\n    const valueContext = useToggleGroupValueContext(ITEM_NAME, props.__scopeToggleGroup);\n    const context = useToggleGroupContext(ITEM_NAME, props.__scopeToggleGroup);\n    const rovingFocusGroupScope = useRovingFocusGroupScope(props.__scopeToggleGroup);\n    const pressed = valueContext.value.includes(props.value);\n    const disabled = context.disabled || props.disabled;\n    const commonProps = { ...props, pressed, disabled };\n    const ref = React.useRef<HTMLDivElement>(null);\n    return context.rovingFocus ? (\n      <RovingFocusGroup.Item\n        asChild\n        {...rovingFocusGroupScope}\n        focusable={!disabled}\n        active={pressed}\n        ref={ref}\n      >\n        <ToggleGroupItemImpl {...commonProps} ref={forwardedRef} />\n      </RovingFocusGroup.Item>\n    ) : (\n      <ToggleGroupItemImpl {...commonProps} ref={forwardedRef} />\n    );\n  }\n);\n\nToggleGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype ToggleGroupItemImplElement = React.ElementRef<typeof Toggle>;\ntype ToggleProps = React.ComponentPropsWithoutRef<typeof Toggle>;\ninterface ToggleGroupItemImplProps extends Omit<ToggleProps, 'defaultPressed' | 'onPressedChange'> {\n  /**\n   * A string value for the toggle group item. All items within a toggle group should use a unique value.\n   */\n  value: string;\n}\n\nconst ToggleGroupItemImpl = React.forwardRef<ToggleGroupItemImplElement, ToggleGroupItemImplProps>(\n  (props: ScopedProps<ToggleGroupItemImplProps>, forwardedRef) => {\n    const { __scopeToggleGroup, value, ...itemProps } = props;\n    const valueContext = useToggleGroupValueContext(ITEM_NAME, __scopeToggleGroup);\n    const singleProps = { role: 'radio', 'aria-checked': props.pressed, 'aria-pressed': undefined };\n    const typeProps = valueContext.type === 'single' ? singleProps : undefined;\n    return (\n      <Toggle\n        {...typeProps}\n        {...itemProps}\n        ref={forwardedRef}\n        onPressedChange={(pressed) => {\n          if (pressed) {\n            valueContext.onItemActivate(value);\n          } else {\n            valueContext.onItemDeactivate(value);\n          }\n        }}\n      />\n    );\n  }\n);\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = ToggleGroup;\nconst Item = ToggleGroupItem;\n\nexport {\n  createToggleGroupScope,\n  //\n  ToggleGroup,\n  ToggleGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { ToggleGroupSingleProps, ToggleGroupMultipleProps, ToggleGroupItemProps };\n", "import * as React from 'react';\n\nfunction createContext<ContextValueType extends object | null>(\n  rootComponentName: string,\n  defaultContext?: ContextValueType\n) {\n  const Context = React.createContext<ContextValueType | undefined>(defaultContext);\n\n  function Provider(props: ContextValueType & { children: React.ReactNode }) {\n    const { children, ...context } = props;\n    // Only re-memoize when prop values change\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n    return <Context.Provider value={value}>{children}</Context.Provider>;\n  }\n\n  function useContext(consumerName: string) {\n    const context = React.useContext(Context);\n    if (context) return context;\n    if (defaultContext !== undefined) return defaultContext;\n    // if a defaultContext wasn't specified, it's a required context.\n    throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n  }\n\n  Provider.displayName = rootComponentName + 'Provider';\n  return [Provider, useContext] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * createContextScope\n * -----------------------------------------------------------------------------------------------*/\n\ntype Scope<C = any> = { [scopeName: string]: React.Context<C>[] } | undefined;\ntype ScopeHook = (scope: Scope) => { [__scopeProp: string]: Scope };\ninterface CreateScope {\n  scopeName: string;\n  (): ScopeHook;\n}\n\nfunction createContextScope(scopeName: string, createContextScopeDeps: CreateScope[] = []) {\n  let defaultContexts: any[] = [];\n\n  /* -----------------------------------------------------------------------------------------------\n   * createContext\n   * ---------------------------------------------------------------------------------------------*/\n\n  function createContext<ContextValueType extends object | null>(\n    rootComponentName: string,\n    defaultContext?: ContextValueType\n  ) {\n    const BaseContext = React.createContext<ContextValueType | undefined>(defaultContext);\n    const index = defaultContexts.length;\n    defaultContexts = [...defaultContexts, defaultContext];\n\n    function Provider(\n      props: ContextValueType & { scope: Scope<ContextValueType>; children: React.ReactNode }\n    ) {\n      const { scope, children, ...context } = props;\n      const Context = scope?.[scopeName][index] || BaseContext;\n      // Only re-memoize when prop values change\n      // eslint-disable-next-line react-hooks/exhaustive-deps\n      const value = React.useMemo(() => context, Object.values(context)) as ContextValueType;\n      return <Context.Provider value={value}>{children}</Context.Provider>;\n    }\n\n    function useContext(consumerName: string, scope: Scope<ContextValueType | undefined>) {\n      const Context = scope?.[scopeName][index] || BaseContext;\n      const context = React.useContext(Context);\n      if (context) return context;\n      if (defaultContext !== undefined) return defaultContext;\n      // if a defaultContext wasn't specified, it's a required context.\n      throw new Error(`\\`${consumerName}\\` must be used within \\`${rootComponentName}\\``);\n    }\n\n    Provider.displayName = rootComponentName + 'Provider';\n    return [Provider, useContext] as const;\n  }\n\n  /* -----------------------------------------------------------------------------------------------\n   * createScope\n   * ---------------------------------------------------------------------------------------------*/\n\n  const createScope: CreateScope = () => {\n    const scopeContexts = defaultContexts.map((defaultContext) => {\n      return React.createContext(defaultContext);\n    });\n    return function useScope(scope: Scope) {\n      const contexts = scope?.[scopeName] || scopeContexts;\n      return React.useMemo(\n        () => ({ [`__scope${scopeName}`]: { ...scope, [scopeName]: contexts } }),\n        [scope, contexts]\n      );\n    };\n  };\n\n  createScope.scopeName = scopeName;\n  return [createContext, composeContextScopes(createScope, ...createContextScopeDeps)] as const;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * composeContextScopes\n * -----------------------------------------------------------------------------------------------*/\n\nfunction composeContextScopes(...scopes: CreateScope[]) {\n  const baseScope = scopes[0];\n  if (scopes.length === 1) return baseScope;\n\n  const createScope: CreateScope = () => {\n    const scopeHooks = scopes.map((createScope) => ({\n      useScope: createScope(),\n      scopeName: createScope.scopeName,\n    }));\n\n    return function useComposedScopes(overrideScopes) {\n      const nextScopes = scopeHooks.reduce((nextScopes, { useScope, scopeName }) => {\n        // We are calling a hook inside a callback which React warns against to avoid inconsistent\n        // renders, however, scoping doesn't have render side effects so we ignore the rule.\n        // eslint-disable-next-line react-hooks/rules-of-hooks\n        const scopeProps = useScope(overrideScopes);\n        const currentScope = scopeProps[`__scope${scopeName}`];\n        return { ...nextScopes, ...currentScope };\n      }, {});\n\n      return React.useMemo(() => ({ [`__scope${baseScope.scopeName}`]: nextScopes }), [nextScopes]);\n    };\n  };\n\n  createScope.scopeName = baseScope.scopeName;\n  return createScope;\n}\n\n/* -----------------------------------------------------------------------------------------------*/\n\nexport { createContext, createContextScope };\nexport type { CreateScope, Scope };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,mBAAkB;;;ACAlB,YAAuB;AAaZ,yBAAA;AA0BX,SAAS,mBAAmB,WAAmB,yBAAwC,CAAC,GAAG;AACzF,MAAI,kBAAyB,CAAC;AAM9B,WAASA,eACP,mBACA,gBACA;AACA,UAAM,cAAoB,oBAA4C,cAAc;AACpF,UAAM,QAAQ,gBAAgB;AAC9B,sBAAkB,CAAC,GAAG,iBAAiB,cAAc;AAErD,aAAS,SACP,OACA;AACA,YAAM,EAAE,OAAO,UAAU,GAAG,QAAQ,IAAI;AACxC,YAAM,WAAU,+BAAQ,WAAW,WAAU;AAG7C,YAAM,QAAc,cAAQ,MAAM,SAAS,OAAO,OAAO,OAAO,CAAC;AACjE,iBAAO,wBAAC,QAAQ,UAAR,EAAiB,OAAe,SAAA,CAAS;IACnD;AAEA,aAASC,YAAW,cAAsB,OAA4C;AACpF,YAAM,WAAU,+BAAQ,WAAW,WAAU;AAC7C,YAAM,UAAgB,iBAAW,OAAO;AACxC,UAAI,QAAS,QAAO;AACpB,UAAI,mBAAmB,OAAW,QAAO;AAEzC,YAAM,IAAI,MAAM,KAAK,YAAY,4BAA4B,iBAAiB,IAAI;IACpF;AAEA,aAAS,cAAc,oBAAoB;AAC3C,WAAO,CAAC,UAAUA,WAAU;EAC9B;AAMA,QAAM,cAA2B,MAAM;AACrC,UAAM,gBAAgB,gBAAgB,IAAI,CAAC,mBAAmB;AAC5D,aAAa,oBAAc,cAAc;IAC3C,CAAC;AACD,WAAO,SAAS,SAAS,OAAc;AACrC,YAAM,YAAW,+BAAQ,eAAc;AACvC,aAAa;QACX,OAAO,EAAE,CAAC,UAAU,SAAS,EAAE,GAAG,EAAE,GAAG,OAAO,CAAC,SAAS,GAAG,SAAS,EAAE;QACtE,CAAC,OAAO,QAAQ;MAClB;IACF;EACF;AAEA,cAAY,YAAY;AACxB,SAAO,CAACD,gBAAe,qBAAqB,aAAa,GAAG,sBAAsB,CAAC;AACrF;AAMA,SAAS,wBAAwB,QAAuB;AACtD,QAAM,YAAY,OAAO,CAAC;AAC1B,MAAI,OAAO,WAAW,EAAG,QAAO;AAEhC,QAAM,cAA2B,MAAM;AACrC,UAAM,aAAa,OAAO,IAAI,CAACE,kBAAiB;MAC9C,UAAUA,aAAY;MACtB,WAAWA,aAAY;IACzB,EAAE;AAEF,WAAO,SAAS,kBAAkB,gBAAgB;AAChD,YAAM,aAAa,WAAW,OAAO,CAACC,aAAY,EAAE,UAAU,UAAU,MAAM;AAI5E,cAAM,aAAa,SAAS,cAAc;AAC1C,cAAM,eAAe,WAAW,UAAU,SAAS,EAAE;AACrD,eAAO,EAAE,GAAGA,aAAY,GAAG,aAAa;MAC1C,GAAG,CAAC,CAAC;AAEL,aAAa,cAAQ,OAAO,EAAE,CAAC,UAAU,UAAU,SAAS,EAAE,GAAG,WAAW,IAAI,CAAC,UAAU,CAAC;IAC9F;EACF;AAEA,cAAY,YAAY,UAAU;AAClC,SAAO;AACT;;;AD1FW,IAAAC,sBAAA;AAxBX,IAAM,oBAAoB;AAG1B,IAAM,CAAC,0BAA0B,sBAAsB,IAAI,mBAAmB,mBAAmB;EAC/F;AACF,CAAC;AACD,IAAM,2BAA2B,4BAA4B;AAU7D,IAAM,cAAc,aAAAC,QAAM,WAGxB,CAAC,OAAO,iBAAiB;AACzB,QAAM,EAAE,MAAM,GAAG,iBAAiB,IAAI;AAEtC,MAAI,SAAS,UAAU;AACrB,UAAM,cAAc;AACpB,eAAO,yBAAC,uBAAA,EAAuB,GAAG,aAAa,KAAK,aAAA,CAAc;EACpE;AAEA,MAAI,SAAS,YAAY;AACvB,UAAM,gBAAgB;AACtB,eAAO,yBAAC,yBAAA,EAAyB,GAAG,eAAe,KAAK,aAAA,CAAc;EACxE;AAEA,QAAM,IAAI,MAAM,uCAAuC,iBAAiB,IAAI;AAC9E,CAAC;AAED,YAAY,cAAc;AAW1B,IAAM,CAAC,0BAA0B,0BAA0B,IACzD,yBAAuD,iBAAiB;AAmB1E,IAAM,wBAAwB,aAAAA,QAAM,WAGlC,CAAC,OAAgD,iBAAiB;AAClE,QAAM;IACJ,OAAO;IACP;IACA,gBAAgB,MAAM;IAAC;IACvB,GAAG;EACL,IAAI;AAEJ,QAAM,CAAC,OAAO,QAAQ,IAAI,qBAAqB;IAC7C,MAAM;IACN,aAAa;IACb,UAAU;EACZ,CAAC;AAED,aACE;IAAC;IAAA;MACC,OAAO,MAAM;MACb,MAAK;MACL,OAAO,QAAQ,CAAC,KAAK,IAAI,CAAC;MAC1B,gBAAgB;MAChB,kBAAkB,aAAAA,QAAM,YAAY,MAAM,SAAS,EAAE,GAAG,CAAC,QAAQ,CAAC;MAElE,cAAA,yBAAC,iBAAA,EAAiB,GAAG,wBAAwB,KAAK,aAAA,CAAc;IAAA;EAClE;AAEJ,CAAC;AAmBD,IAAM,0BAA0B,aAAAA,QAAM,WAGpC,CAAC,OAAkD,iBAAiB;AACpE,QAAM;IACJ,OAAO;IACP;IACA,gBAAgB,MAAM;IAAC;IACvB,GAAG;EACL,IAAI;AAEJ,QAAM,CAAC,QAAQ,CAAC,GAAG,QAAQ,IAAI,qBAAqB;IAClD,MAAM;IACN,aAAa;IACb,UAAU;EACZ,CAAC;AAED,QAAM,uBAAuB,aAAAA,QAAM;IACjC,CAAC,cAAsB,SAAS,CAAC,YAAY,CAAC,MAAM,CAAC,GAAG,WAAW,SAAS,CAAC;IAC7E,CAAC,QAAQ;EACX;AAEA,QAAM,yBAAyB,aAAAA,QAAM;IACnC,CAAC,cACC,SAAS,CAAC,YAAY,CAAC,MAAM,UAAU,OAAO,CAACC,WAAUA,WAAU,SAAS,CAAC;IAC/E,CAAC,QAAQ;EACX;AAEA,aACE;IAAC;IAAA;MACC,OAAO,MAAM;MACb,MAAK;MACL;MACA,gBAAgB;MAChB,kBAAkB;MAElB,cAAA,yBAAC,iBAAA,EAAiB,GAAG,0BAA0B,KAAK,aAAA,CAAc;IAAA;EACpE;AAEJ,CAAC;AAED,YAAY,cAAc;AAM1B,IAAM,CAAC,oBAAoB,qBAAqB,IAC9C,yBAAkD,iBAAiB;AAqBrE,IAAM,kBAAkB,aAAAD,QAAM;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM;MACJ;MACA,WAAW;MACX,cAAc;MACd;MACA;MACA,OAAO;MACP,GAAG;IACL,IAAI;AACJ,UAAM,wBAAwB,yBAAyB,kBAAkB;AACzE,UAAM,YAAY,aAAa,GAAG;AAClC,UAAM,cAAc,EAAE,MAAM,SAAS,KAAK,WAAW,GAAG,iBAAiB;AACzE,eACE,yBAAC,oBAAA,EAAmB,OAAO,oBAAoB,aAA0B,UACtE,UAAA,kBACC;MAAkB;MAAjB;QACC,SAAO;QACN,GAAG;QACJ;QACA,KAAK;QACL;QAEA,cAAA,yBAAC,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,aAAA,CAAc;MAAA;IACrD,QAEA,yBAAC,UAAU,KAAV,EAAe,GAAG,aAAa,KAAK,aAAA,CAAc,EAAA,CAEvD;EAEJ;AACF;AAMA,IAAM,YAAY;AAKlB,IAAM,kBAAkB,aAAAA,QAAM;EAC5B,CAAC,OAA0C,iBAAiB;AAC1D,UAAM,eAAe,2BAA2B,WAAW,MAAM,kBAAkB;AACnF,UAAM,UAAU,sBAAsB,WAAW,MAAM,kBAAkB;AACzE,UAAM,wBAAwB,yBAAyB,MAAM,kBAAkB;AAC/E,UAAM,UAAU,aAAa,MAAM,SAAS,MAAM,KAAK;AACvD,UAAM,WAAW,QAAQ,YAAY,MAAM;AAC3C,UAAM,cAAc,EAAE,GAAG,OAAO,SAAS,SAAS;AAClD,UAAM,MAAM,aAAAA,QAAM,OAAuB,IAAI;AAC7C,WAAO,QAAQ,kBACb;MAAkB;MAAjB;QACC,SAAO;QACN,GAAG;QACJ,WAAW,CAAC;QACZ,QAAQ;QACR;QAEA,cAAA,yBAAC,qBAAA,EAAqB,GAAG,aAAa,KAAK,aAAA,CAAc;MAAA;IAC3D,QAEA,yBAAC,qBAAA,EAAqB,GAAG,aAAa,KAAK,aAAA,CAAc;EAE7D;AACF;AAEA,gBAAgB,cAAc;AAa9B,IAAM,sBAAsB,aAAAA,QAAM;EAChC,CAAC,OAA8C,iBAAiB;AAC9D,UAAM,EAAE,oBAAoB,OAAO,GAAG,UAAU,IAAI;AACpD,UAAM,eAAe,2BAA2B,WAAW,kBAAkB;AAC7E,UAAM,cAAc,EAAE,MAAM,SAAS,gBAAgB,MAAM,SAAS,gBAAgB,OAAU;AAC9F,UAAM,YAAY,aAAa,SAAS,WAAW,cAAc;AACjE,eACE;MAAC;MAAA;QACE,GAAG;QACH,GAAG;QACJ,KAAK;QACL,iBAAiB,CAAC,YAAY;AAC5B,cAAI,SAAS;AACX,yBAAa,eAAe,KAAK;UACnC,OAAO;AACL,yBAAa,iBAAiB,KAAK;UACrC;QACF;MAAA;IACF;EAEJ;AACF;AAIA,IAAME,QAAO;AACb,IAAMC,QAAO;", "names": ["createContext", "useContext", "createScope", "nextScopes", "import_jsx_runtime", "React", "value", "Root", "<PERSON><PERSON>"]}