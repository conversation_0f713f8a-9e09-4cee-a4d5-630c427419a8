import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { HelpCircle, RefreshCw } from 'lucide-react';
import { useGlassContext } from '@/context/GlassContext';

interface SharedLayoutProps {
  children: React.ReactNode;
  showTutorial?: () => void;
}

const SharedLayout: React.FC<SharedLayoutProps> = ({ children, showTutorial }) => {
  const location = useLocation();
  const isGlassApp = location.pathname === '/';
  const isMetalApp = location.pathname === '/metal-fab';

  // Only use glass context if we're on the glass app
  const glassContext = isGlassApp ? useGlassContext() : null;

  return (
    <div className="container px-4 py-8 max-w-6xl mx-auto relative overflow-x-hidden">
      <header className="mb-8 flex justify-between items-center">
        <div className="flex items-center gap-4">
          {showTutorial && (
            <Button
              variant="outline"
              size="sm"
              onClick={showTutorial}
              className="text-white border-[#4b5563] hover:bg-[#243447] hover:text-white"
            >
              <HelpCircle className="h-4 w-4 mr-2" />
              Tutorial
            </Button>
          )}

          {isGlassApp && glassContext && (
            <Button
              variant="outline"
              size="sm"
              onClick={glassContext.resetGlassTypes}
              className="text-white border-[#4b5563] hover:bg-[#243447] hover:text-white"
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Reset Glass Types
            </Button>
          )}

          <div className="flex space-x-2">
            <Button
              variant={isGlassApp ? "default" : "outline"}
              size="sm"
              className={isGlassApp
                ? "bg-primary text-white"
                : "text-white border-[#4b5563] hover:bg-[#243447] hover:text-white"}
              asChild
            >
              <Link to="/">
                Glass Pricing
              </Link>
            </Button>

            <Button
              variant={isMetalApp ? "default" : "outline"}
              size="sm"
              className={isMetalApp
                ? "bg-primary text-white"
                : "text-white border-[#4b5563] hover:bg-[#243447] hover:text-white"}
              asChild
            >
              <Link to="/metal-fab">
                Metal Fab
              </Link>
            </Button>
          </div>
        </div>

        <img
          src="/servicepanelogo.png"
          alt="ServicePane Logo"
          className="h-20 object-contain"
        />
      </header>

      <div className="glass-card p-6 rounded-lg relative overflow-hidden min-h-screen">
        {children}
      </div>
    </div>
  );
};

export default SharedLayout;
