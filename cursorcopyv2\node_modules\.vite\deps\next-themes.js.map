{"version": 3, "sources": ["../../next-themes/dist/index.mjs"], "sourcesContent": ["\"use client\";import*as t from\"react\";var P=[\"light\",\"dark\"],E=\"(prefers-color-scheme: dark)\",Q=typeof window==\"undefined\",L=t.createContext(void 0),D={setTheme:e=>{},themes:[]},j=()=>{var e;return(e=t.useContext(L))!=null?e:D},z=e=>t.useContext(L)?e.children:t.createElement(O,{...e}),N=[\"light\",\"dark\"],O=({forcedTheme:e,disableTransitionOnChange:a=!1,enableSystem:n=!0,enableColorScheme:g=!0,storageKey:m=\"theme\",themes:c=N,defaultTheme:o=n?\"system\":\"light\",attribute:y=\"data-theme\",value:h,children:k,nonce:w})=>{let[i,d]=t.useState(()=>M(m,o)),[S,l]=t.useState(()=>M(m)),u=h?Object.values(h):c,R=t.useCallback(s=>{let r=s;if(!r)return;s===\"system\"&&n&&(r=T());let v=h?h[r]:r,C=a?_():null,x=document.documentElement;if(y===\"class\"?(x.classList.remove(...u),v&&x.classList.add(v)):v?x.setAttribute(y,v):x.removeAttribute(y),g){let I=P.includes(o)?o:null,A=P.includes(r)?r:I;x.style.colorScheme=A}C==null||C()},[]),f=t.useCallback(s=>{let r=typeof s==\"function\"?s(s):s;d(r);try{localStorage.setItem(m,r)}catch(v){}},[e]),p=t.useCallback(s=>{let r=T(s);l(r),i===\"system\"&&n&&!e&&R(\"system\")},[i,e]);t.useEffect(()=>{let s=window.matchMedia(E);return s.addListener(p),p(s),()=>s.removeListener(p)},[p]),t.useEffect(()=>{let s=r=>{if(r.key!==m)return;let v=r.newValue||o;f(v)};return window.addEventListener(\"storage\",s),()=>window.removeEventListener(\"storage\",s)},[f]),t.useEffect(()=>{R(e!=null?e:i)},[e,i]);let $=t.useMemo(()=>({theme:i,setTheme:f,forcedTheme:e,resolvedTheme:i===\"system\"?S:i,themes:n?[...c,\"system\"]:c,systemTheme:n?S:void 0}),[i,f,e,S,n,c]);return t.createElement(L.Provider,{value:$},t.createElement(U,{forcedTheme:e,disableTransitionOnChange:a,enableSystem:n,enableColorScheme:g,storageKey:m,themes:c,defaultTheme:o,attribute:y,value:h,children:k,attrs:u,nonce:w}),k)},U=t.memo(({forcedTheme:e,storageKey:a,attribute:n,enableSystem:g,enableColorScheme:m,defaultTheme:c,value:o,attrs:y,nonce:h})=>{let k=c===\"system\",w=n===\"class\"?`var d=document.documentElement,c=d.classList;${`c.remove(${y.map(u=>`'${u}'`).join(\",\")})`};`:`var d=document.documentElement,n='${n}',s='setAttribute';`,i=m?(P.includes(c)?c:null)?`if(e==='light'||e==='dark'||!e)d.style.colorScheme=e||'${c}'`:\"if(e==='light'||e==='dark')d.style.colorScheme=e\":\"\",d=(l,u=!1,R=!0)=>{let f=o?o[l]:l,p=u?l+\"|| ''\":`'${f}'`,$=\"\";return m&&R&&!u&&P.includes(l)&&($+=`d.style.colorScheme = '${l}';`),n===\"class\"?u||f?$+=`c.add(${p})`:$+=\"null\":f&&($+=`d[s](n,${p})`),$},S=e?`!function(){${w}${d(e)}}()`:g?`!function(){try{${w}var e=localStorage.getItem('${a}');if('system'===e||(!e&&${k})){var t='${E}',m=window.matchMedia(t);if(m.media!==t||m.matches){${d(\"dark\")}}else{${d(\"light\")}}}else if(e){${o?`var x=${JSON.stringify(o)};`:\"\"}${d(o?\"x[e]\":\"e\",!0)}}${k?\"\":\"else{\"+d(c,!1,!1)+\"}\"}${i}}catch(e){}}()`:`!function(){try{${w}var e=localStorage.getItem('${a}');if(e){${o?`var x=${JSON.stringify(o)};`:\"\"}${d(o?\"x[e]\":\"e\",!0)}}else{${d(c,!1,!1)};}${i}}catch(t){}}();`;return t.createElement(\"script\",{nonce:h,dangerouslySetInnerHTML:{__html:S}})}),M=(e,a)=>{if(Q)return;let n;try{n=localStorage.getItem(e)||void 0}catch(g){}return n||a},_=()=>{let e=document.createElement(\"style\");return e.appendChild(document.createTextNode(\"*{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")),document.head.appendChild(e),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(e)},1)}},T=e=>(e||(e=window.matchMedia(E)),e.matches?\"dark\":\"light\");export{z as ThemeProvider,j as useTheme};\n"], "mappings": ";;;;;;;;;AAAa,QAAgB;AAAQ,IAAI,IAAE,CAAC,SAAQ,MAAM;AAArB,IAAuB,IAAE;AAAzB,IAAwD,IAAE,OAAO,UAAQ;AAAzE,IAAqF,IAAI,gBAAc,MAAM;AAA7G,IAA+G,IAAE,EAAC,UAAS,OAAG;AAAC,GAAE,QAAO,CAAC,EAAC;AAA1I,IAA4I,IAAE,MAAI;AAAC,MAAI;AAAE,UAAO,IAAI,aAAW,CAAC,MAAI,OAAK,IAAE;AAAC;AAA5L,IAA8L,IAAE,OAAK,aAAW,CAAC,IAAE,EAAE,WAAW,gBAAc,GAAE,EAAC,GAAG,EAAC,CAAC;AAAtP,IAAwP,IAAE,CAAC,SAAQ,MAAM;AAAzQ,IAA2Q,IAAE,CAAC,EAAC,aAAY,GAAE,2BAA0B,IAAE,OAAG,cAAa,IAAE,MAAG,mBAAkB,IAAE,MAAG,YAAW,IAAE,SAAQ,QAAO,IAAE,GAAE,cAAa,IAAE,IAAE,WAAS,SAAQ,WAAU,IAAE,cAAa,OAAM,GAAE,UAAS,GAAE,OAAM,EAAC,MAAI;AAAC,MAAG,CAAC,GAAE,CAAC,IAAI,WAAS,MAAI,EAAE,GAAE,CAAC,CAAC,GAAE,CAAC,GAAE,CAAC,IAAI,WAAS,MAAI,EAAE,CAAC,CAAC,GAAE,IAAE,IAAE,OAAO,OAAO,CAAC,IAAE,GAAE,IAAI,cAAY,OAAG;AAAC,QAAI,IAAE;AAAE,QAAG,CAAC,EAAE;AAAO,UAAI,YAAU,MAAI,IAAE,EAAE;AAAG,QAAI,IAAE,IAAE,EAAE,CAAC,IAAE,GAAE,IAAE,IAAE,EAAE,IAAE,MAAK,IAAE,SAAS;AAAgB,QAAG,MAAI,WAAS,EAAE,UAAU,OAAO,GAAG,CAAC,GAAE,KAAG,EAAE,UAAU,IAAI,CAAC,KAAG,IAAE,EAAE,aAAa,GAAE,CAAC,IAAE,EAAE,gBAAgB,CAAC,GAAE,GAAE;AAAC,UAAI,IAAE,EAAE,SAAS,CAAC,IAAE,IAAE,MAAK,IAAE,EAAE,SAAS,CAAC,IAAE,IAAE;AAAE,QAAE,MAAM,cAAY;AAAA,IAAC;AAAC,SAAG,QAAM,EAAE;AAAA,EAAC,GAAE,CAAC,CAAC,GAAE,IAAI,cAAY,OAAG;AAAC,QAAI,IAAE,OAAO,KAAG,aAAW,EAAE,CAAC,IAAE;AAAE,MAAE,CAAC;AAAE,QAAG;AAAC,mBAAa,QAAQ,GAAE,CAAC;AAAA,IAAC,SAAO,GAAE;AAAA,IAAC;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAE,IAAI,cAAY,OAAG;AAAC,QAAI,IAAE,EAAE,CAAC;AAAE,MAAE,CAAC,GAAE,MAAI,YAAU,KAAG,CAAC,KAAG,EAAE,QAAQ;AAAA,EAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAE,EAAE,YAAU,MAAI;AAAC,QAAI,IAAE,OAAO,WAAW,CAAC;AAAE,WAAO,EAAE,YAAY,CAAC,GAAE,EAAE,CAAC,GAAE,MAAI,EAAE,eAAe,CAAC;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAI,YAAU,MAAI;AAAC,QAAI,IAAE,OAAG;AAAC,UAAG,EAAE,QAAM,EAAE;AAAO,UAAI,IAAE,EAAE,YAAU;AAAE,QAAE,CAAC;AAAA,IAAC;AAAE,WAAO,OAAO,iBAAiB,WAAU,CAAC,GAAE,MAAI,OAAO,oBAAoB,WAAU,CAAC;AAAA,EAAC,GAAE,CAAC,CAAC,CAAC,GAAI,YAAU,MAAI;AAAC,MAAE,KAAG,OAAK,IAAE,CAAC;AAAA,EAAC,GAAE,CAAC,GAAE,CAAC,CAAC;AAAE,MAAI,IAAI,UAAQ,OAAK,EAAC,OAAM,GAAE,UAAS,GAAE,aAAY,GAAE,eAAc,MAAI,WAAS,IAAE,GAAE,QAAO,IAAE,CAAC,GAAG,GAAE,QAAQ,IAAE,GAAE,aAAY,IAAE,IAAE,OAAM,IAAG,CAAC,GAAE,GAAE,GAAE,GAAE,GAAE,CAAC,CAAC;AAAE,SAAS,gBAAc,EAAE,UAAS,EAAC,OAAM,EAAC,GAAI,gBAAc,GAAE,EAAC,aAAY,GAAE,2BAA0B,GAAE,cAAa,GAAE,mBAAkB,GAAE,YAAW,GAAE,QAAO,GAAE,cAAa,GAAE,WAAU,GAAE,OAAM,GAAE,UAAS,GAAE,OAAM,GAAE,OAAM,EAAC,CAAC,GAAE,CAAC;AAAC;AAAztD,IAA2tD,IAAI,OAAK,CAAC,EAAC,aAAY,GAAE,YAAW,GAAE,WAAU,GAAE,cAAa,GAAE,mBAAkB,GAAE,cAAa,GAAE,OAAM,GAAE,OAAM,GAAE,OAAM,EAAC,MAAI;AAAC,MAAI,IAAE,MAAI,UAAS,IAAE,MAAI,UAAQ,gDAAgD,YAAY,EAAE,IAAI,OAAG,IAAI,CAAC,GAAG,EAAE,KAAK,GAAG,CAAC,GAAG,MAAI,qCAAqC,CAAC,uBAAsB,IAAE,KAAG,EAAE,SAAS,CAAC,IAAE,IAAE,QAAM,0DAA0D,CAAC,MAAI,qDAAmD,IAAG,IAAE,CAAC,GAAE,IAAE,OAAG,IAAE,SAAK;AAAC,QAAI,IAAE,IAAE,EAAE,CAAC,IAAE,GAAE,IAAE,IAAE,IAAE,UAAQ,IAAI,CAAC,KAAI,IAAE;AAAG,WAAO,KAAG,KAAG,CAAC,KAAG,EAAE,SAAS,CAAC,MAAI,KAAG,0BAA0B,CAAC,OAAM,MAAI,UAAQ,KAAG,IAAE,KAAG,SAAS,CAAC,MAAI,KAAG,SAAO,MAAI,KAAG,UAAU,CAAC,MAAK;AAAA,EAAC,GAAE,IAAE,IAAE,eAAe,CAAC,GAAG,EAAE,CAAC,CAAC,QAAM,IAAE,mBAAmB,CAAC,+BAA+B,CAAC,4BAA4B,CAAC,aAAa,CAAC,uDAAuD,EAAE,MAAM,CAAC,SAAS,EAAE,OAAO,CAAC,gBAAgB,IAAE,SAAS,KAAK,UAAU,CAAC,CAAC,MAAI,EAAE,GAAG,EAAE,IAAE,SAAO,KAAI,IAAE,CAAC,IAAI,IAAE,KAAG,UAAQ,EAAE,GAAE,OAAG,KAAE,IAAE,GAAG,GAAG,CAAC,mBAAiB,mBAAmB,CAAC,+BAA+B,CAAC,YAAY,IAAE,SAAS,KAAK,UAAU,CAAC,CAAC,MAAI,EAAE,GAAG,EAAE,IAAE,SAAO,KAAI,IAAE,CAAC,SAAS,EAAE,GAAE,OAAG,KAAE,CAAC,KAAK,CAAC;AAAkB,SAAS,gBAAc,UAAS,EAAC,OAAM,GAAE,yBAAwB,EAAC,QAAO,EAAC,EAAC,CAAC;AAAC,CAAC;AAA/6F,IAAi7F,IAAE,CAAC,GAAE,MAAI;AAAC,MAAG,EAAE;AAAO,MAAI;AAAE,MAAG;AAAC,QAAE,aAAa,QAAQ,CAAC,KAAG;AAAA,EAAM,SAAO,GAAE;AAAA,EAAC;AAAC,SAAO,KAAG;AAAC;AAAxgG,IAA0gG,IAAE,MAAI;AAAC,MAAI,IAAE,SAAS,cAAc,OAAO;AAAE,SAAO,EAAE,YAAY,SAAS,eAAe,0JAA0J,CAAC,GAAE,SAAS,KAAK,YAAY,CAAC,GAAE,MAAI;AAAC,WAAO,iBAAiB,SAAS,IAAI,GAAE,WAAW,MAAI;AAAC,eAAS,KAAK,YAAY,CAAC;AAAA,IAAC,GAAE,CAAC;AAAA,EAAC;AAAC;AAA33G,IAA63G,IAAE,QAAI,MAAI,IAAE,OAAO,WAAW,CAAC,IAAG,EAAE,UAAQ,SAAO;", "names": []}