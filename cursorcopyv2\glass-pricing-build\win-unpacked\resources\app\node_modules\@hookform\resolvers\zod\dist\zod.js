var r=require("@hookform/resolvers"),e=require("react-hook-form"),o=function(r,o){for(var n={};r.length;){var s=r[0],t=s.code,i=s.message,a=s.path.join(".");if(!n[a])if("unionErrors"in s){var u=s.unionErrors[0].errors[0];n[a]={message:u.message,type:u.code}}else n[a]={message:i,type:t};if("unionErrors"in s&&s.unionErrors.forEach(function(e){return e.errors.forEach(function(e){return r.push(e)})}),o){var c=n[a].types,f=c&&c[s.code];n[a]=e.appendErrors(a,o,n,t,f?[].concat(f,s.message):s.message)}r.shift()}return n};exports.zodResolver=function(e,n,s){return void 0===s&&(s={}),function(t,i,a){try{return Promise.resolve(function(o,i){try{var u=Promise.resolve(e["sync"===s.mode?"parse":"parseAsync"](t,n)).then(function(e){return a.shouldUseNativeValidation&&r.validateFieldsNatively({},a),{errors:{},values:s.raw?t:e}})}catch(r){return i(r)}return u&&u.then?u.then(void 0,i):u}(0,function(e){if(function(r){return Array.isArray(null==r?void 0:r.errors)}(e))return{values:{},errors:r.toNestErrors(o(e.errors,!a.shouldUseNativeValidation&&"all"===a.criteriaMode),a)};throw e}))}catch(r){return Promise.reject(r)}}};
//# sourceMappingURL=zod.js.map
