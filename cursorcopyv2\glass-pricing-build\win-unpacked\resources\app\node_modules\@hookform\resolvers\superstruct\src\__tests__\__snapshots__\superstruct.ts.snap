// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`superstructResolver > should return a single error from superstructResolver when validation fails 1`] = `
{
  "errors": {
    "birthYear": {
      "message": "Expected a number, but received: "birthYear"",
      "ref": undefined,
      "type": "number",
    },
    "email": {
      "message": "Expected a string matching \`/^[\\w-\\.]+@([\\w-]+\\.)+[\\w-]{2,4}$/\` but received """,
      "ref": {
        "name": "email",
      },
      "type": "string",
    },
    "enabled": {
      "message": "Expected a value of type \`boolean\`, but received: \`undefined\`",
      "ref": undefined,
      "type": "boolean",
    },
    "like": [
      {
        "id": {
          "message": "Expected a number, but received: "z"",
          "ref": undefined,
          "type": "number",
        },
        "name": {
          "message": "Expected a string, but received: undefined",
          "ref": undefined,
          "type": "string",
        },
      },
    ],
    "password": {
      "message": "Expected a string matching \`/^[a-zA-Z0-9]{3,30}/\` but received "___"",
      "ref": {
        "name": "password",
      },
      "type": "string",
    },
    "repeatPassword": {
      "message": "Expected a value of type \`Password\`, but received: \`undefined\`",
      "ref": undefined,
      "type": "Password",
    },
    "tags": {
      "message": "Expected an array value, but received: undefined",
      "ref": undefined,
      "type": "array",
    },
    "username": {
      "message": "Expected a string, but received: undefined",
      "ref": {
        "name": "username",
      },
      "type": "string",
    },
  },
  "values": {},
}
`;
