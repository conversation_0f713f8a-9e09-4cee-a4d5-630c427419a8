import{toNestErrors as e,validateFieldsNatively as r}from"@hookform/resolvers";import{appendErrors as t}from"react-hook-form";const a=(a,o={abortEarly:!1},s={})=>async(i,l,c)=>{const n=Object.assign({},o,{context:l});let d={};if("sync"===s.mode)d=a.validate(i,n);else try{d.value=await a.validateAsync(i,n)}catch(e){d.error=e}return d.error?{values:{},errors:e((m=d.error,v=!c.shouldUseNativeValidation&&"all"===c.criteriaMode,m.details.length?m.details.reduce((e,r)=>{const a=r.path.join(".");if(e[a]||(e[a]={message:r.message,type:r.type}),v){const o=e[a].types,s=o&&o[r.type];e[a]=t(a,v,e,r.type,s?[].concat(s,r.message):r.message)}return e},{}):{}),c)}:(c.shouldUseNativeValidation&&r({},c),{errors:{},values:d.value});var m,v};export{a as joiResolver};
//# sourceMappingURL=joi.modern.mjs.map
